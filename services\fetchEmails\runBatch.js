const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Configuration
const inputDir = path.join( './499');
const maxConcurrentProcesses = 6;
const defaultStartRow = 0;
const defaultEndRow = 999; // Process 1000 rows per file
const defaultBatchSize = 10;

// Ensure the input directory exists
if (!fs.existsSync(inputDir)) {
  console.error(`Input directory "${inputDir}" does not exist.`);
  process.exit(1);
}

// Get all CSV files from the input directory
const csvFiles = fs.readdirSync(inputDir)
  .filter(file => file.toLowerCase().endsWith('.csv'));

if (csvFiles.length === 0) {
  console.error(`No CSV files found in "${inputDir}".`);
  process.exit(1);
}

console.log(`Found ${csvFiles.length} CSV files to process.`);

// Queue to manage running processes
let runningProcesses = 0;
const queue = [...csvFiles];

// Function to run the email fetcher for a single file
function processFile(csvFile) {
  const inputFilePath = path.join(inputDir, csvFile);
  const outputFileName = `${path.basename(csvFile, '.csv')}_output.csv`;
  const outputFilePath = path.join(inputDir, outputFileName);
  
  console.log(`Starting process for: ${csvFile}`);
  console.log(`Output will be saved to: ${outputFileName}`);
  
  const process = spawn('node', [
    path.join(__dirname, 'index.js'),
    inputFilePath,
    outputFilePath,
    defaultStartRow.toString(),
    defaultEndRow.toString(),
    defaultBatchSize.toString()
  ]);
  
  runningProcesses++;
  
  process.stdout.on('data', (data) => {
    console.log(`[${csvFile}] ${data.toString().trim()}`);
  });
  
  process.stderr.on('data', (data) => {
    console.error(`[${csvFile}] ERROR: ${data.toString().trim()}`);
  });
  
  process.on('close', (code) => {
    console.log(`Process for ${csvFile} completed with code ${code}`);
    runningProcesses--;
    
    // Process next file in queue if available
    if (queue.length > 0) {
      const nextFile = queue.shift();
      processFile(nextFile);
    } else if (runningProcesses === 0) {
      console.log('All files have been processed.');
    }
  });
}

// Start initial batch of processes
const initialBatchSize = Math.min(maxConcurrentProcesses, queue.length);
for (let i = 0; i < initialBatchSize; i++) {
  const file = queue.shift();
  processFile(file);
}