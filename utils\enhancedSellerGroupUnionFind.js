const prisma = require("../database/prisma/getPrismaClient");

  /**
   * Find parent group ID for an entity
   * @param {string} entityType - 'domain' or 'seller_id'
   * @param {string} entityValue - The actual value
   * @returns {number|null} Parent group ID or null if not found
   */
    async function findParentGroup(entityType, entityValue) {
    const entity = await prisma.sellerTree.findUnique({
      where: {
        entity_type_entity_value: {
          entity_type: entityType.toLowerCase(),
          entity_value: entityValue
        }
      },
      select: { parent_id: true }
    });

    return entity?.parent_id || null;
  }

  /**
   * Create an entity in the seller tree
   * @param {string} entityType - 'domain' or 'seller_id'
   * @param {string} entityValue - The actual value
   * @param {number} parentGroupId - Parent group ID
   */
  async function createEntity(entityType, entityValue, parentGroupId) {
    await prisma.sellerTree.create({
      data: {
        entity_type: entityType.toLowerCase(),
        entity_value: entityValue,
        parent_id: parentGroupId
      }
    });
  }

  /**
   * Merge two seller groups
   * @param {Object} winningGroup - The group that will remain
   * @param {Object} losingGroup - The group that will be merged into winner
   */
  async function mergeGroups(winningGroup, losingGroup) {
    console.log(`🔄 Merging group ${losingGroup.id} into group ${winningGroup.id}`);

    if (!winningGroup || !losingGroup) {
      throw new Error(`Cannot merge: One or both groups not found`);
    }

    // Choose winner based on rank
    const { winner, loser, shouldUpdateRank } = await chooseWinner(winningGroup, losingGroup);
    
    // Get the actual group objects for winner and loser
    const finalWinner = winner === winningGroup.id ? winningGroup : losingGroup;
    const finalLoser = loser === losingGroup.id ? losingGroup : winningGroup;

    // Merge arrays
    const mergedSellerIds = [...new Set([
      ...(Array.isArray(finalWinner.seller_ids) ? finalWinner.seller_ids : []),
      ...(Array.isArray(finalLoser.seller_ids) ? finalLoser.seller_ids : [])
    ])];

    const mergedDomains = [...new Set([
      ...(Array.isArray(finalWinner.domains) ? finalWinner.domains : []),
      ...(Array.isArray(finalLoser.domains) ? finalLoser.domains : [])
    ])];

    await prisma.$transaction(async (tx) => {
      // Update winning group with merged data
      const newRank = shouldUpdateRank ? 
        Math.max(finalWinner.rank || 0, finalLoser.rank || 0) + 1 : 
        finalWinner.rank || 0;

      await tx.sellerGroup.update({
        where: { id: finalWinner.id },
        data: {
          seller_ids: mergedSellerIds,
          domains: mergedDomains,
          name: `MERGED_SELLER_GROUP`,
          rank: newRank,
          domain_count: mergedDomains.length,
          seller_count: mergedSellerIds.length
        }
      });

      // Move all tree entries to winning group
      await tx.sellerTree.updateMany({
        where: { parent_id: finalLoser.id },
        data: { parent_id: finalWinner.id }
      });

      // Move all sellers to winning group
      await tx.amazonSeller.updateMany({
        where: { seller_group_id: finalLoser.id },
        data: { seller_group_id: finalWinner.id }
      });

      // Delete losing group
      await tx.sellerGroup.delete({
        where: { id: loser }
      });
    });

    console.log(`✅ Successfully merged group ${finalLoser.id} into ${finalWinner.id}`);
  }

  /**
   * Choose winning group based on rank from sellerGroup
   * @param {Object} group1 - First group object with rank
   * @param {Object} group2 - Second group object with rank
   * @returns {Object} {winner, loser, shouldUpdateRank}
   */
  async function chooseWinner(group1, group2) {
    if (!group1 || !group2) {
      throw new Error(`Cannot choose winner: One or both groups not found`);
    }

    const rank1 = group1.rank || 0;
    const rank2 = group2.rank || 0;

    let winner, loser, shouldUpdateRank = false;
    
    if (rank1 > rank2) {
      winner = group1.id;
      loser = group2.id;
    } else if (rank1 < rank2) {
      winner = group2.id;
      loser = group1.id;
    } else {
      // Equal rank - choose based on seller count as tiebreaker
      const [count1, count2] = await Promise.all([
        prisma.amazonSeller.count({ where: { seller_group_id: group1.id } }),
        prisma.amazonSeller.count({ where: { seller_group_id: group2.id } })
      ]);

      if (count1 >= count2) {
        winner = group1.id;
        loser = group2.id;
      } else {
        winner = group2.id;
        loser = group1.id;
      }
      
      shouldUpdateRank = true;
    }

    return { winner, loser, shouldUpdateRank };
  }

/**
 * Process a single seller based on the simplified logic
 * @param {Object} seller - Seller object
 * @returns {Object} Processing result
 */
async function processSeller(seller, validationOptions = {}) {
  const sellerId = seller.amazon_seller_id;
  const domain = seller.domain?.toLowerCase().trim();
  const currentGroupId = seller.seller_group_id;
  
  // Domain validation criteria
  const { validWebsiteStatuses = ['Final Correct'] } = validationOptions;
  const isDomainValid = domain && 
                       domain !== '' && 
                       seller.website_status && 
                       validWebsiteStatuses.includes(seller.website_status);
  
  // Find parent groups for seller_id and domain (only if domain is valid)
  const sellerParent = sellerId ? await findParentGroup('seller_id', sellerId) : null;
  const domainParent = isDomainValid ? await findParentGroup('domain', domain) : null;

  let result = {
    action: 'none',
    merged: false,
    created: [],
    groupId: currentGroupId
  };

  if (sellerParent && domainParent) {
    // Both entities exist
    if (sellerParent === domainParent) {
      // Same group - check if matches current
      if (sellerParent === currentGroupId) {
        result.action = 'already_grouped';
      } else {
        result.action = 'group_mismatch';
        result.groupId = sellerParent;
        
        // Update seller to point to correct group
        await prisma.amazonSeller.update({
          where: { id: seller.id },
          data: { seller_group_id: sellerParent }
        });
      }
    } else {
      // Different groups - need to merge
      result.action = 'merge_required';
      
      // Get both groups
      const [group1, group2] = await Promise.all([
        prisma.sellerGroup.findUnique({
          where: { id: sellerParent },
          select: { id: true, rank: true, seller_ids: true, domains: true, name: true }
        }),
        prisma.sellerGroup.findUnique({
          where: { id: domainParent },
          select: { id: true, rank: true, seller_ids: true, domains: true, name: true }
        })
      ]);
      
      await mergeGroups(group1, group2);
      result.merged = true;
      result.groupId = group1.id; // Will be updated by mergeGroups if different
      
      // Update seller to point to winning group (will be determined by mergeGroups)
      const finalGroupId = await findParentGroup('seller_id', sellerId) || 
                          await findParentGroup('domain', domain);
      if (finalGroupId) {
        await prisma.amazonSeller.update({
          where: { id: seller.id },
          data: { seller_group_id: finalGroupId }
        });
        result.groupId = finalGroupId;
      }
    }
  } else if (sellerParent || domainParent) {
    // One entity exists
    const existingGroup = sellerParent || domainParent;
    
    if (existingGroup === currentGroupId) {
      // Create missing entity
      if (sellerId && !sellerParent) {
        await createEntity('seller_id', sellerId, existingGroup);
        result.created.push('seller_id');
      }
      if (isDomainValid && !domainParent) {
        await createEntity('domain', domain, existingGroup);
        result.created.push('domain');
      }
      result.action = 'created_missing';
    } else {
      // Group mismatch - merge needed if currentGroupId exists
      if (currentGroupId) {
        result.action = 'merge_with_existing';
        
        // Get both groups
        const [existingGroupObj, currentGroupObj] = await Promise.all([
          prisma.sellerGroup.findUnique({
            where: { id: existingGroup },
            select: { id: true, rank: true, seller_ids: true, domains: true, name: true }
          }),
          prisma.sellerGroup.findUnique({
            where: { id: currentGroupId },
            select: { id: true, rank: true, seller_ids: true, domains: true, name: true }
          })
        ]);
        
        await mergeGroups(existingGroupObj, currentGroupObj);
        result.merged = true;
        result.groupId = existingGroup; // Will be updated by mergeGroups if different
        
        // Create missing entity in winning group
        const finalGroupId = await findParentGroup('seller_id', sellerId) || 
                            await findParentGroup('domain', domain);
        if (finalGroupId) {
          if (sellerId && !sellerParent) {
            await createEntity('seller_id', sellerId, finalGroupId);
            result.created.push('seller_id');
          }
          if (isDomainValid && !domainParent) {
            await createEntity('domain', domain, finalGroupId);
            result.created.push('domain');
          }
          
          // Update seller to point to winning group
          await prisma.amazonSeller.update({
            where: { id: seller.id },
            data: { seller_group_id: finalGroupId }
          });
          result.groupId = finalGroupId;
        }
      } else {
        // No current group - just assign to existing group
        result.action = 'assign_to_existing';
        result.groupId = existingGroup;
        
        // Create missing entity
        if (sellerId && !sellerParent) {
          await createEntity('seller_id', sellerId, existingGroup);
          result.created.push('seller_id');
        }
        if (isDomainValid && !domainParent) {
          await createEntity('domain', domain, existingGroup);
          result.created.push('domain');
        }
        
        // Update seller to point to existing group
        await prisma.amazonSeller.update({
          where: { id: seller.id },
          data: { seller_group_id: existingGroup }
        });
      }
    }
  } else {
    // No entities exist - create both with current group (or create new group if none)
    result.action = 'create_both';
    
    if (!currentGroupId) {
      // Seller has no group - create a new one
      currentGroupId = await prisma.sellerGroup.create({
        data: {
          name: `Group_${sellerId || (isDomainValid ? domain : 'unknown')}`,
          seller_ids: sellerId ? [sellerId] : [],
          domains: isDomainValid ? [domain] : [],
          domain_count: isDomainValid ? 1 : 0,
          seller_count: sellerId ? 1 : 0,
          rank: 0
        }
            
      });
      result.groupId = currentGroupId;
      
      // Update seller with new group
      await prisma.amazonSeller.update({
        where: { id: seller.id },
        data: { seller_group_id: currentGroupId }
      });
      
      result.action = 'create_new_group';
    }
    
    if (sellerId) {
      await createEntity('seller_id', sellerId, currentGroupId);
      result.created.push('seller_id');
    }
    if (isDomainValid) {
      await createEntity('domain', domain, currentGroupId);
      result.created.push('domain');
    }
  }

  return result;
}

/**
 * Clear all seller tree data (for fresh migration)
 * @param {Object} options - Options
 */
async function clearSellerTreeData(options = {}) {
  const { dryRun = false } = options;

  if (dryRun) {
    const counts = await Promise.all([
      prisma.sellerTree.count(),
      prisma.sellerGroup.count()
    ]);
    
    console.log(`DRY RUN: Would clear ${counts[0]} seller tree entries and ${counts[1]} seller groups`);
    return { cleared: { sellerTrees: counts[0], sellerGroups: counts[1] } };
  }

  console.log("Clearing existing seller tree data...");
  
  try {
    await prisma.$transaction(async (tx) => {
      // Clear seller group assignments from sellers
      await tx.amazonSeller.updateMany({
        data: { seller_group_id: null }
      });

      // Delete all seller tree entries
      await tx.sellerTree.deleteMany({});

      // Delete all seller groups
      await tx.sellerGroup.deleteMany({});
    });

    console.log("Seller tree data cleared successfully");
  } catch (error) {
    console.error("Error clearing seller tree data:", error.message);
    throw error;
  }
  
  return { cleared: true };
}

module.exports = {
  findParentGroup,
  createEntity,
  mergeGroups,
  chooseWinner,
  processSeller,
  clearSellerTreeData
};