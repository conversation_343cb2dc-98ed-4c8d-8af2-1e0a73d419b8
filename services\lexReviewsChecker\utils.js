// reviewChecker.js
const axios = require("axios");
const cheerio = require("cheerio");
require("dotenv").config();
const SCRAPER_API_KEY = process.env.SCRAPER_API_KEY;
const { ReviewStatus } = require("@prisma/client");

const fs = require("fs");
const path = require("path");

const MAX_RETRIES = 3;
const MAX_REMOVED_FILES = 10;
const DEBUG_DIR = path.join(__dirname, "debug_removed_html");

if (!fs.existsSync(DEBUG_DIR)) {
  fs.mkdirSync(DEBUG_DIR);
}

async function processReview(url, attempt = 1, isReverify = false) {
  try {
    console.log(
      `Checking [Attempt ${attempt}]${isReverify ? " [Reverify]" : ""}: ${url}`
    );
    const response = await axios.get(url, {
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept-Language": "en-US,en;q=0.9",
      },
      proxy: {
        host: "proxy-server.scraperapi.com",
        port: 8001,
        auth: {
          username: "scraperapi",
          password: SCRAPER_API_KEY,
        },
        protocol: "http",
      },
      validateStatus: () => true,
    });

    const html = response.data;
    const status = response.status;

    if (status === 404) {
      console.log(`404 Not Found: ${url} -> REMOVED`);
      if (!isReverify) {
        console.log("Re-verifying because REMOVED detected...");
        return await processReview(url, attempt, true);
      }
      await saveRemovedHTML(url, html);
      return ReviewStatus.REMOVED;
    }

    if (status !== 200) {
      console.warn(`Non-200 status (${status}) on ${url}`);
      if (attempt < MAX_RETRIES) {
        console.log("Retrying...");
        await sleep(1000 * attempt);
        return processReview(url, attempt + 1);
      }
      return ReviewStatus.FAILED;
    }

    if (/captcha|Robot Check|Enter the characters you see below/i.test(html)) {
      console.warn(`Captcha detected on ${url}`);
      if (attempt < MAX_RETRIES) {
        console.log("Retrying due to captcha...");
        await sleep(2000 * attempt);
        return processReview(url, attempt + 1);
      }
      return ReviewStatus.FAILED;
    }

    const hasReviewList = html.includes("cm_cr-review_list");
    const noReviewsText = /No customer reviews|has no reviews/i.test(html);
    const emptyReviewSection = /cm_cr-review_list[^>]*>\s*<\/div>/.test(html);

    const detectedRemoved =
      !hasReviewList || noReviewsText || emptyReviewSection;

    if (detectedRemoved && attempt < MAX_RETRIES && !isReverify) {
      console.log("Suspicious REMOVED detected — retrying to confirm...");
      await sleep(1500 * attempt);
      return processReview(url, attempt + 1);
    }

    if (detectedRemoved) {
      if (!isReverify) {
        console.log("Re-verifying because REMOVED detected...");
        return await processReview(url, attempt, true);
      }
      console.log("Confirmed REMOVED after reverify.");
      await saveRemovedHTML(url, html);
      return ReviewStatus.REMOVED;
    }

    console.log(`ReviewLink: ${url}, Final Decision: ${ReviewStatus.PRESENT}`);
    return ReviewStatus.PRESENT;
  } catch (error) {
    console.error(`Error on attempt ${attempt} for ${url}:`, error.message);
    if (attempt < MAX_RETRIES) {
      console.log("Retrying due to error...");
      await sleep(1000 * attempt);
      return processReview(url, attempt + 1);
    }
    return ReviewStatus.FAILED;
  }
}

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function saveRemovedHTML(url, html) {
  const reviewId = extractReviewId(url);
  if (!reviewId) {
    console.warn(`Could not extract Review ID from URL: ${url}`);
    return;
  }

  const fileName = `${reviewId}.html`;
  const filePath = path.join(DEBUG_DIR, fileName);

  try {
    await fs.promises.writeFile(filePath, html);
    console.log(`Saved REMOVED review HTML to: ${filePath}`);
    await enforceMaxRemovedFiles();
  } catch (err) {
    console.error(`Failed to save HTML for ${url}:`, err.message);
  }
}

function extractReviewId(url) {
  const match = url.match(/\/([^\/?#]+)(?:[\/?#]|$)/);
  return match ? match[1] : null;
}

async function enforceMaxRemovedFiles() {
  try {
    const files = await fs.promises.readdir(DEBUG_DIR);

    const fileStats = await Promise.all(
      files.map(async (file) => {
        const filePath = path.join(DEBUG_DIR, file);
        const stats = await fs.promises.stat(filePath);
        return { file, time: stats.birthtimeMs || stats.ctimeMs };
      })
    );

    fileStats.sort((a, b) => a.time - b.time);

    if (fileStats.length > MAX_REMOVED_FILES) {
      const filesToDelete = fileStats.slice(
        0,
        fileStats.length - MAX_REMOVED_FILES
      );
      for (const { file } of filesToDelete) {
        const filePath = path.join(DEBUG_DIR, file);
        await fs.promises.unlink(filePath);
        console.log(`Deleted old REMOVED review HTML: ${filePath}`);
      }
    }
  } catch (err) {
    console.error("Failed to enforce max removed files limit:", err.message);
  }
}

async function example() {
  const res = await processReview(
    "https://www.amazon.com/gp/customer-reviews/RL0WBL4WOU904"
  );
  console.log(res);
}
// example();


async function sendLexSlackNotification(
  reviews,
  reportType,
  customMessage = "Starting Script",
  totalCheckedToday,
  numOfFailedChecks,
  customSlackMessage = null
) {
  const webhookUrl = process.env.LEX_NOTI_SLACK_WEBHOOK_URL;
  const TAGGED_USERS = ["<@U088F8THDT6>", "<@U0765PL0W4V>", "<@U08JABT9WD9>"];

  if (!webhookUrl) {
    console.warn("Slack webhook URL not set");
    return;
  }

  let message = {};

  // If custom message is provided, use it directly
  if (customSlackMessage) {
    message = customSlackMessage;
  } else {
    switch (reportType) {
      case "DAILY":
        // Handle daily report with multiple reviews
        const reviewsText = reviews
          .map(
            (review) =>
              `*Review Details:*\n` +
              `• *Review ID:* ${review.reviewId}\n` +
              `• *Review Job ID:* ${review.reviewJobId}\n` +
              `• *ASIN:* \`${review.asin}\`\n` +
              `• *Brand:* ${review.brandName}\n` +
              `• *Status:* "REMOVED"\n` +
              `• *Review Link:* <${review.reviewUrl}|View Review>\n` +
              `• *Created At:* ${new Date(review.createdAt).toLocaleString()}\n` +
              `• *Updated At:* ${new Date(review.updatedAt).toLocaleString()}\n`
          )
          .join("\n");

        message = {
          text:
            `:clipboard: *Daily Lex Review Checker Report* ${TAGGED_USERS?.join(" ")}\n\n` +
            `*Total Reviews Checked Today:* ${totalCheckedToday}\n` +
            `*Removed Reviews Found:* ${reviews.length}\n\n` +
            `*Failed Checks:* ${numOfFailedChecks}\n\n` +
            `${reviews.length > 0 ? reviewsText : "No removed reviews found."}`,
        };
        break;

      case "PING":
        // Handle ping messages for script status
        message = {
          text:
            `:bell: *Lex Review Checker Status* ${TAGGED_USERS?.join(" ")}\n` +
            `*Total Reviews Found :* ${totalCheckedToday}\n` +
            `${customMessage}`,
        };
        break;

      case "RESURRECTED":
        // Handle resurrected review notifications
        message = {
          text: customSlackMessage?.text || "Resurrected review notification",
        };
        break;

      default:
        // Handle single review notification
        message = {
          text:
            `:rotating_light: *Review Removed!* ${TAGGED_USERS?.join(" ")}\n\n` +
            `*Review ID:* ${reviews.reviewId}\n` +
            `*Review Job ID:* ${reviews.reviewJobId}\n` +
            `*ASIN:* \`${reviews.asin}\`\n` +
            `*Brand:* ${reviews.brandName}\n` +
            `*Status:* "REMOVED"\n` +
            `*Review Link:* <${reviews.reviewUrl}|View Review>\n` +
            `*Created At:* ${new Date(reviews.createdAt).toLocaleString()}\n` +
            `*Updated At:* ${new Date(reviews.updatedAt).toLocaleString()}`,
        };
    }
  }
  // Send the message to Slack
  try {
    await axios.post(webhookUrl, message);
    console.log("SLACK MESSAGE", message);
    console.log(`Slack notification sent`);
  } catch (error) {
    console.error("Error sending Slack notification:", error);
  }
}

module.exports = { processReview, sendLexSlackNotification }
