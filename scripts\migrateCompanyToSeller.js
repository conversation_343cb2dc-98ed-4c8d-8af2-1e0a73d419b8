const prisma = require("../database/prisma/getPrismaClient");
const { extractDomain } = require("../utils/domainHelper");

// Configuration
const BATCH_SIZE = 2000; // Process more companies per batch
const MAX_TRANSACTION_SIZE = 500; // Max records in a single transaction

// Add these conversion functions at the top of the file
function convertToFloat(value) {
  if (value === null || value === undefined || value === '') return null;
  const num = parseFloat(value);
  return isNaN(num) ? null : num;
}

function convertToInt(value) {
  if (value === null || value === undefined || value === '') return null;
  const num = parseInt(value, 10);
  return isNaN(num) ? null : num;
}

function convertToDate(value) {
  if (value === null || value === undefined || value === '') return null;
  const date = new Date(value);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * Main migration function that processes companies in batches
 */
async function migrateCompanyToSeller() {
  console.log('Starting optimized migration...');
  
  // Preload ALL seller country matchings into memory (reduce DB hits)
  const allSellerCountryMatchings = await prisma.sellerCountryMatching.findMany();
  const matchingsBySellerID = allSellerCountryMatchings.reduce((acc, match) => {
    if (!acc[match.amazon_seller_id]) acc[match.amazon_seller_id] = [];
    acc[match.amazon_seller_id].push(match);
    return acc;
  }, {});
  
  // Stats tracking
  let companiesProcessed = 0;
  let sellerGroupsCreated = 0;
  let sellersCreated = 0;
  let sellersUpdated = 0;
  
  // Process in large batches
  let skip = 0;
  let hasMore = true;
  
  while (hasMore) {
    // Get a batch of companies
    const companies = await fetchCompanyBatch(skip, BATCH_SIZE);
    if (companies.length === 0) {
      hasMore = false;
      continue;
    }
    
    companiesProcessed += companies.length;
    // 1. CREATE ALL SELLER GROUPS IN ONE TRANSACTION
    const sellerGroupData = companies.map(company => ({
      name: company.name || `Group-${company.amazon_seller_id}`
    }));
    
    const sellerGroups = await prisma.$transaction(
      sellerGroupData.map(data => prisma.sellerGroup.create({ data }))
    );
    
    sellerGroupsCreated += sellerGroups.length;
    
    // Create lookup for companies and their seller groups
    const sellerGroupsByCompanyId = {};
    companies.forEach((company, index) => {
      sellerGroupsByCompanyId[company.id] = sellerGroups[index].id;
    });
    
    // 2. CREATE/UPDATE SELLERS IN BULK TRANSACTIONS
    const sellerCreates = [];
    const sellerUpdates = [];
    
    // Get all existing sellers for this batch to avoid duplicates
    const sellerIds = companies.map(c => c.amazon_seller_id).filter(Boolean);
    const existingSellers = await prisma.amazonSeller.findMany({
      where: { amazon_seller_id: { in: sellerIds } }
    });
    
    // Map existing sellers by ID and marketplace for quick lookup
    const existingSellerMap = {};
    existingSellers.forEach(seller => {
      const key = `${seller.amazon_seller_id}_${seller.marketplace}`;
      existingSellerMap[key] = seller;
    });
    
    // Prepare seller operations
    companies.forEach(company => {
      const countryMatches = matchingsBySellerID[company.amazon_seller_id] || [];
      const sellerGroupId = sellerGroupsByCompanyId[company.id];
      
      // If no country matches, still create a seller with null marketplace
      if (countryMatches.length === 0) {
        const sellerData = mapCompanyToSellerData(company, null, sellerGroupId);
        sellerCreates.push(sellerData);
      } else {
        countryMatches.forEach(match => {
          const sellerData = mapCompanyToSellerData(company, match.smartscout_country, sellerGroupId);
          const lookupKey = `${company.amazon_seller_id}_${match.smartscout_country}`;
          
          if (existingSellerMap[lookupKey]) {
            sellerUpdates.push({
              where: { id: existingSellerMap[lookupKey].id },
              data: sellerData
            });
          } else {
            sellerCreates.push(sellerData);
          }
        });
      }
    });
    
    // Execute seller creates in batches with error handling
    for (let i = 0; i < sellerCreates.length; i += MAX_TRANSACTION_SIZE) {
      const batch = sellerCreates.slice(i, i + MAX_TRANSACTION_SIZE);
      try {
        // Use createMany with skipDuplicates option
        await prisma.amazonSeller.createMany({
          data: batch,
          skipDuplicates: true
        });
        sellersCreated += batch.length;
      } catch (error) {
        // If createMany fails, process items individually
        console.log(`Batch operation failed, processing individually: ${error.message}`);
        for (const data of batch) {
          try {
            // First check if this seller already exists to avoid the constraint error
            const existing = await prisma.amazonSeller.findFirst({
              where: {
                amazon_seller_id: data.amazon_seller_id,
                marketplace: data.marketplace
              }
            });
            
            if (!existing) {
              await prisma.amazonSeller.create({ data });
              sellersCreated++;
            } else {
              // Update instead of create
              await prisma.amazonSeller.update({
                where: { id: existing.id },
                data
              });
              sellersUpdated++;
            }
          } catch (itemError) {
            console.error(`Error processing seller ${data.amazon_seller_id}/${data.marketplace}: ${itemError.message}`);
          }
        }
      }
    }
    
    // Execute seller updates in batches
    for (let i = 0; i < sellerUpdates.length; i += MAX_TRANSACTION_SIZE) {
      const batch = sellerUpdates.slice(i, i + MAX_TRANSACTION_SIZE);
      await prisma.$transaction(batch.map(op => prisma.amazonSeller.update(op)));
      sellersUpdated += batch.length;
    }
    
    console.log(`Processed ${companies.length} companies, created ${sellerGroups.length} seller groups, created ${sellerCreates.length} sellers, updated ${sellerUpdates.length} sellers, total processed: ${companiesProcessed}`);
    
    skip += BATCH_SIZE;
  }
  
  // Print final stats
  console.log('\nMigration completed:');
  console.log(`Total companies processed: ${companiesProcessed}`);
  console.log(`Total seller groups created: ${sellerGroupsCreated}`);
  console.log(`Total sellers created: ${sellersCreated}`);
  console.log(`Total sellers updated: ${sellersUpdated}`);
}

/**
 * Fetch a batch of companies
 * @param {number} skip - Number of records to skip
 * @param {number} take - Number of records to take
 * @returns {Promise<Array>} - Array of company records
 */
async function fetchCompanyBatch(skip, take) {
  return prisma.company.findMany({
    skip,
    take,
    orderBy: { id: 'asc' }
  });
}

// Helper function to map company data to seller data
function mapCompanyToSellerData(company, marketplace, sellerGroupId) {
  return {
    name: company.name || null,
    amazon_seller_id: company.amazon_seller_id || null,
    marketplace: marketplace || null,
    seller_group_id: sellerGroupId,
    // Rest of field mappings with type conversions
    primary_category: company.primary_category || null,
    primary_sub_category: company.primary_sub_category || null,
    estimate_sales: convertToFloat(company.derived_estimate_sales),
    avg_price: null, // No corresponding field in company
    percent_fba: convertToFloat(company.percent_fba),
    number_reviews_lifetime: null, // No corresponding field in company
    number_reviews_30days: null, // No corresponding field in company
    number_winning_brands: convertToInt(company.number_winning_brands),
    number_asins: convertToInt(company.number_asins),
    number_top_asins: convertToInt(company.number_top_asins),
    business_name: company.business_name || null,
    number_brands_1000: convertToInt(company.number_brands_1000),
    mom_growth: convertToFloat(company.mom_growth),
    mom_growth_count: convertToInt(company.mom_growth_count),
    started_selling_date: convertToDate(company.started_selling_date),
    domain: extractDomain(company.website),
    website_status: company.website_status || null,
    lookup_source: company.lookup_source || null,
    lookup_sources: company.lookup_sources || {},
    // Map address fields
    street: company.company_address || null,
    city: company.company_location || null,
    adr_state: company.state || null,
    adr_country: company.country || null,
    adr_zip_code: company.company_pincode || null,
  };
}

// Run the migration if this file is executed directly
if (require.main === module) {
  migrateCompanyToSeller()
    .then(() => {
      console.log('Migration script completed successfully.');
      process.exit(0);
    })
    .catch(err => {
      console.error('Migration failed:', err);
      process.exit(1);
    });
}

// Export functions for testing or use in other modules
module.exports = {
  migrateCompanyToSeller,
  fetchCompanyBatch,
  mapCompanyToSellerData
};
