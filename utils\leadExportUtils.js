const converter = require("json-2-csv");
const path = require("path");
const fs = require("fs");
const { createMultiSheetFromCsvs } = require("./googleSheetUtils");
const prisma = require("../database/prisma/getPrismaClient");
const pricingConfig = require("../config/pricing");
const os = require("os");
const { Transform } = require("stream");
const createCsvWriter = require("csv-writer").createObjectCsvWriter;
const archiver = require("archiver");

/**
 * Checks if a URL is considered valid based on its validation results
 * @param {Object} url - The URL object to check
 * @returns {Boolean} - True if the URL is valid, false otherwise
 */
const isValidUrl = (url) => {
  if (!url) return false;
  const addressMatch = url.hasAddressMatch;
  const hasImageMatch = url.hasImageMatch;
  const hasTextMatch = url.hasTextMatch;
  const hasNumberMatch = url.hasNumberMatch;
  const hasEmailMatch = url.hasEmailMatch;
  return (
    addressMatch ||
    hasImageMatch ||
    hasTextMatch ||
    hasNumberMatch ||
    hasEmailMatch
  );
};

/**
 * Checks if a URL is considered successful based on confidence or validation
 * @param {Object} url - The URL object to check
 * @returns {Boolean} - True if the URL is successful, false otherwise
 */
const isUrlSuccessful = (url) => {
  if (!url) return false;
  return (
    isValidUrl(url) || parseFloat(url.confidence || url?.Confidence || 0) > 5.5
  );
};

/**
 * Checks if a lead is considered successful based on its URLs
 * @param {Object} lead - The lead object to check
 * @returns {Boolean} - True if the lead is successful, false otherwise
 */
const isLeadSuccessful = (lead) => {
  if (
    !lead ||
    !lead.urls ||
    !Array.isArray(lead.urls) ||
    lead.urls.length === 0
  ) {
    return false;
  }
  const validUrls = lead.urls.filter((url) => url && (url.url || url.domain));
  return validUrls.some(isUrlSuccessful);
};

/**
 * Calculates the total tokens used for text and image validation for a lead
 * @param {Object} lead - The lead object
 * @returns {Object} - An object with token counts for text and image validation
 */
const getTotalTokens = (lead) => {
  return lead.urls.reduce(
    (prev, curr) => {
      // Handle text validation
      let textPromptTokens = 0;
      let textCompletionTokens = 0;
      let textTotalTokens = 0;

      if (Array.isArray(curr.textValidation)) {
        // New format (array)
        curr.textValidation.forEach((validation) => {
          textPromptTokens += validation.result?.prompt_tokens || 0;
          textCompletionTokens += validation.result?.completion_tokens || 0;
          textTotalTokens += validation.result?.total_tokens || 0;
        });
      } else if (curr.textValidation) {
        // Old format (object)
        textPromptTokens =
          curr.textValidation?.prompt_tokens ||
          curr.textValidation?.prompt_tokens_details?.cached_tokens ||
          0;
        textCompletionTokens = curr.textValidation?.completion_tokens || 0;
        textTotalTokens = curr.textValidation?.total_tokens || 0;
      }

      // Handle image validation
      let imagePromptTokens = 0;
      let imageCompletionTokens = 0;
      let imageTotalTokens = 0;

      if (Array.isArray(curr.imageValidation)) {
        // New format (array)
        curr.imageValidation.forEach((validation) => {
          imagePromptTokens += validation.result?.prompt_tokens || 0;
          imageCompletionTokens += validation.result?.completion_tokens || 0;
          imageTotalTokens += validation.result?.total_tokens || 0;
        });
      } else if (curr.imageValidation || curr.ImageValidation) {
        // Old format (object)
        imagePromptTokens =
          curr.imageValidation?.prompt_tokens ||
          curr.ImageValidation?.prompt_tokens ||
          0;
        imageCompletionTokens =
          curr.imageValidation?.completion_tokens ||
          curr.ImageValidation?.completion_tokens ||
          0;
        imageTotalTokens =
          curr.imageValidation?.total_tokens ||
          curr.ImageValidation?.total_tokens ||
          0;
      }

      return {
        textValidation: {
          tokens: prev.textValidation.tokens + textTotalTokens,
          prompt_tokens: prev.textValidation.prompt_tokens + textPromptTokens,
          completion_tokens:
            prev.textValidation.completion_tokens + textCompletionTokens,
        },
        imageValidation: {
          tokens: prev.imageValidation.tokens + imageTotalTokens,
          prompt_tokens: prev.imageValidation.prompt_tokens + imagePromptTokens,
          completion_tokens:
            prev.imageValidation.completion_tokens + imageCompletionTokens,
        },
      };
    },
    {
      textValidation: {
        tokens: 0,
        prompt_tokens: 0,
        completion_tokens: 0,
      },
      imageValidation: {
        tokens: 0,
        prompt_tokens: 0,
        completion_tokens: 0,
      },
    }
  );
};

/**
 * Creates row data for CSV export from a lead and optional URL
 * @param {Object} lead - The lead object
 * @param {Object|null} url - Optional URL object
 * @param {Array} counter - Array containing [urlIndex, hasSuccessfulUrl] where urlIndex tracks the URL position and hasSuccessfulUrl indicates if any URL for this lead is successful
 * @returns {Object} - Row data for CSV export
 */
const createRowData = (lead, url = null, counter = [false]) => {
  const metadata = lead.metadata || {};

  const urlColumns = [
    "Job ID",
    "Seller Name",
    "Business Name",
    "Country",
    "Search String",
    "Keywords",
    "Confident Url",
    "Confidence",
    "Seller Url",
    "Company Address",
    "Google Position",
    "Filter Domain Position",
    "discoveredPages",
    "hasNumberMatch",
    "hasAddressMatch",
    "hasTextMatch",
    "hasImageMatch",
    "hasEmailMatch",
    "matchedTextUrls",
    "matchedNumberUrls",
    "matchedAddressUrls",
    "matchedEmailUrls",
    "matchedImageUrls",
    "numberValidation_Success",
    "numberValidation_FinalScore",
    "numberValidation_MatchedKeywords",
    "HTMLCheck_FinalScore",
    "HTMLCheck_MatchedKeywords",
    "SnippetCheck_FinalScore",
    "SnippetCheck_MatchedKeywords",
    "FuzzyDomainCheck_FinalScore",
    "FuzzyDomainCheck_MatchedKeywords",
    "AddressValidation_FinalScore",
    "AddressValidation_BestConfidence",
    "AddressValidation_MatchedAddress",
    "TextValidation_Message",
    "ImageValidation_Message",
    "promptCostText",
    "completionCostText",
    "totalTextCost",
    "promptCostImage",
    "completionCostImage",
    "totalImageCost",
    "totalGPTCost",
  ];

  const urlMetadata = Object.keys(metadata).reduce((acc, key) => {
    if (!urlColumns.includes(key)) {
      acc[key] = metadata[key];
    }
    return acc;
  }, {});

  // Check if URL is successful and update the counter
  const urlStatus = isUrlSuccessful(url);
  counter[0] = counter[0] || urlStatus; // Mark as successful if any URL is successful
  if (!url) {
    const leadColumns = [
      "Job ID",
      "Seller Name",
      "Business Name",
      "Country",
      "textTokens",
      "promptCostText",
      "completionCostText",
      "totalTextCost",
      "promptCostImage",
      "completionCostImage",
      "totalImageCost",
      "totalGPTCost",
      "imageTokens",
      "Status",
      "Seller Url",
      "Company Address",
      "API Response",
      "TextValidationResult",
      "ImageValidationResult",
      "NumberValidationResult",
      "AddressValidationResult",
      "EmailValidationResult",
    ];

    const leadMetadata = Object.keys(metadata).reduce((acc, key) => {
      if (!leadColumns.includes(key)) {
        acc[key] = metadata[key];
      }
      return acc;
    }, {});
    const { textValidation, imageValidation } = getTotalTokens(lead);
    const promptTextCost = pricingConfig.getInputTokenCost(
      textValidation.prompt_tokens
    );
    const completionTextCost = pricingConfig.getOutputTokenCost(
      textValidation.completion_tokens
    );
    const totalTextCost = promptTextCost + completionTextCost;
    const promptImageCost = pricingConfig.getInputTokenCost(
      imageValidation.prompt_tokens
    );
    const completionImageCost = pricingConfig.getOutputTokenCost(
      imageValidation.completion_tokens
    );
    const totalImageCost = promptImageCost + completionImageCost;
    // const phoneValidation = lead.phoneValidation || {};
    const apiResponse =
      lead.apiResponse?.results?.results?.organic.map((result) => ({
        title: result.title,
        link: result.link,
      })) || [];

    return {
      "Job ID": lead.jobId,
      "Seller Name": lead.sellerName,
      "Business Name": lead.businessName,
      Country: lead.country,
      "Search String": lead.searchString || "",
      textTokens: JSON.stringify(textValidation),
      promptCostText: promptTextCost,
      completionCostText: completionTextCost,
      totalTextCost: totalTextCost,
      promptCostImage: promptImageCost,
      completionCostImage: completionImageCost,
      totalImageCost: totalImageCost,
      totalGPTCost: totalTextCost + totalImageCost,
      imageTokens: JSON.stringify(imageValidation),
      Status: counter[0] ? "success" : "failure",
      "Seller Url": lead.sellerUrl,
      "Company Address": lead.address,
      "API Response": JSON.stringify(apiResponse),
      ...leadMetadata,
    };
  }
  // Process number validation
  let numberValidationSuccess = false;
  let numberValidationFinalScore = -1;
  let numberValidationMatchedKeywords = "NONE";

  if (Array.isArray(url.numberValidation)) {
    // New format (array)
    numberValidationSuccess = url.hasNumberMatch;
    // For backward compatibility still calculate these values
    numberValidationFinalScore = url.numberValidation.reduce((max, v) => {
      const score = v.result?.finalScore || 0;
      return score > max ? score : max;
    }, -1);
    // Collect all matched keywords
    const matchedKeywords = url.numberValidation.flatMap(
      (v) => v.result?.matchedKeywords || []
    );
    numberValidationMatchedKeywords = matchedKeywords.length
      ? matchedKeywords.join(" | ")
      : "NONE";
  } else {
    // Old format (object)
    const numberValidation = url.numberValidation || {};
    numberValidationSuccess = (Number(numberValidation?.finalScore) || 0) > 0;
    numberValidationFinalScore = numberValidation?.finalScore || -1;
    numberValidationMatchedKeywords =
      numberValidation?.matchedKeywords?.join(" | ") || "NONE";
  }

  // Process address validation
  let addressValidationFinalScore = -1;
  let addressValidationBestConfidence = -1;
  let addressValidationMatchedAddress = "NONE";

  if (Array.isArray(url.addressValidation)) {
    // New format (array)
    // Use hasAddressMatch flag directly
    addressValidationFinalScore = url.addressValidation.reduce((max, v) => {
      const score = v.result?.finalScore || 0;
      return score > max ? score : max;
    }, -1);
    addressValidationBestConfidence = url.addressValidation.reduce((max, v) => {
      const confidence = v.result?.bestConfidence || 0;
      return confidence > max ? confidence : max;
    }, -1);

    // Find the first address that matched
    const matchedAddressObj = url.addressValidation.find(
      (v) => v.result?.matchedAddress
    );
    addressValidationMatchedAddress =
      matchedAddressObj?.result?.matchedAddress || "NONE";
  } else {
    // Old format (object)
    const addressValidation = url.addressValidation || {};
    addressValidationFinalScore = addressValidation?.finalScore || -1;
    addressValidationBestConfidence = addressValidation?.bestConfidence || -1;
    addressValidationMatchedAddress =
      addressValidation?.matchedAddress || "NONE";
  }

  // Process text validation
  let textValidationMessage = "NONE";

  if (Array.isArray(url.textValidation)) {
    // New format (array) - use the hasTextMatch flag directly
    if (url.hasTextMatch) {
      textValidationMessage = "TRUE";
    } else {
      // Still check if any validation has a result
      const matchedText = url.textValidation.find(
        (v) => v.isMatch || v.result?.message?.toLowerCase() === "true"
      );
      const unmatchedText = url.textValidation.find(
        (v) => !v.isMatch && v.result?.message?.toLowerCase() === "false"
      );
      textValidationMessage = matchedText
        ? "TRUE"
        : unmatchedText
          ? "FALSE"
          : "NONE";
    }
  } else {
    // Old format (object)
    textValidationMessage = url.textValidation?.message || "NONE";
  }

  // Process image validation
  let imageValidationMessage = "NONE";

  if (Array.isArray(url.imageValidation)) {
    // New format (array) - use the hasImageMatch flag directly
    if (url.hasImageMatch) {
      imageValidationMessage = "TRUE";
    } else {
      // Still check if any validation has a positive result for backward compatibility
      const matchedImage = url.imageValidation.find(
        (v) => v.isMatch || v.result?.message?.toLowerCase() === "true"
      );
      const unmatchedImage = url.imageValidation.find(
        (v) => !v.isMatch && v.result?.message?.toLowerCase() === "false"
      );
      imageValidationMessage = matchedImage
        ? "TRUE"
        : unmatchedImage
          ? "FALSE"
          : "NONE";
    }
  } else {
    // Old format (object)
    imageValidationMessage =
      url.imageValidation?.message || url?.ImageValidation?.message || "NONE";
  }

  // Process email validation
  let emailValidationSuccess = false;
  let emailValidationMatchedKeywords = "NONE";

  if (Array.isArray(url.emailValidation)) {
    // New format (array) - use the hasEmailMatch flag directly
    emailValidationSuccess = url.hasEmailMatch;
    // Collect all matched keywords for backward compatibility
    const matchedKeywords = url.emailValidation.flatMap(
      (v) => v.result?.matchedKeywords || []
    );
    emailValidationMatchedKeywords = matchedKeywords.length
      ? matchedKeywords.join(" | ")
      : "NONE";
  } else {
    // Old format (object)
    const emailValidation = url.emailValidation || {};
    emailValidationSuccess = emailValidation?.finalScore > 0;
    emailValidationMatchedKeywords =
      emailValidation?.matchedKeywords?.join(" | ") || "NONE";
  }

  const htmlCheckResult = url.htmlCheckResult || {};
  const snippetCheckResult = url.snippetCheckResult || {};
  const fuzzyDomainResult = url.fuzzyDomainResult || {};

  // Calculate token costs
  let textValidationObj = url.textValidation || {};
  let imageValidationObj = url.imageValidation || url.ImageValidation || {};

  let promptTextCost = 0;
  let completionTextCost = 0;
  let promptImageCost = 0;
  let completionImageCost = 0;

  if (Array.isArray(textValidationObj)) {
    // Sum tokens from all array elements
    const totalPromptTokens = textValidationObj.reduce(
      (sum, v) => sum + (v.result?.prompt_tokens || 0),
      0
    );
    const totalCompletionTokens = textValidationObj.reduce(
      (sum, v) => sum + (v.result?.completion_tokens || 0),
      0
    );
    promptTextCost = pricingConfig.getInputTokenCost(totalPromptTokens);
    completionTextCost = pricingConfig.getOutputTokenCost(
      totalCompletionTokens
    );
  } else {
    promptTextCost = pricingConfig.getInputTokenCost(
      textValidationObj.prompt_tokens || 0
    );
    completionTextCost = pricingConfig.getOutputTokenCost(
      textValidationObj.completion_tokens || 0
    );
  }

  if (Array.isArray(imageValidationObj)) {
    // Sum tokens from all array elements
    const totalPromptTokens = imageValidationObj.reduce(
      (sum, v) => sum + (v.result?.prompt_tokens || 0),
      0
    );
    const totalCompletionTokens = imageValidationObj.reduce(
      (sum, v) => sum + (v.result?.completion_tokens || 0),
      0
    );
    promptImageCost = pricingConfig.getInputTokenCost(totalPromptTokens);
    completionImageCost = pricingConfig.getOutputTokenCost(
      totalCompletionTokens
    );
  } else {
    promptImageCost = pricingConfig.getInputTokenCost(
      imageValidationObj.prompt_tokens || 0
    );
    completionImageCost = pricingConfig.getOutputTokenCost(
      imageValidationObj.completion_tokens || 0
    );
  }

  const totalTextCost = promptTextCost + completionTextCost;
  const totalImageCost = promptImageCost + completionImageCost;

  return {
    "Job ID": lead.jobId,
    "Seller Name": lead.sellerName,
    "Business Name": lead.businessName,
    Country: lead.country,
    "Search String": lead.searchString || "",
    Keywords: JSON.stringify(url.keywords || []),
    // Status: isUrlSuccessful(url) ? "success" : "failure",
    "Confident Url": url.useDomain
      ? url.domain || ""
      : url.url || url.domain || "",
    Confidence: url.confidence,
    "Seller Url": lead.sellerUrl,
    "Company Address": lead.address,
    "Google Position": url.googlePosition,
    "Filter Domain Position": url.filterPosition,
    discoveredPages: JSON.stringify(url.discoveredPages || []),
    "URL Status": urlStatus ? "success" : "failure",
    hasNumberMatch: url.hasNumberMatch,
    hasAddressMatch: url.hasAddressMatch,
    hasTextMatch: url.hasTextMatch,
    hasImageMatch: url.hasImageMatch,
    hasEmailMatch: url.hasEmailMatch,
    matchedTextUrls: JSON.stringify(url.matchedTextUrls || []),
    matchedNumberUrls: JSON.stringify(url.matchedNumberUrls || []),
    matchedAddressUrls: JSON.stringify(url.matchedAddressUrls || []),
    matchedEmailUrls: JSON.stringify(url.matchedEmailUrls || []),
    matchedImageUrls: JSON.stringify(url.matchedImageUrls || []),
    numberValidation_Success: numberValidationSuccess || "FALSE",
    numberValidation_FinalScore: numberValidationFinalScore,
    numberValidation_MatchedKeywords: numberValidationMatchedKeywords,
    AddressValidation_FinalScore: addressValidationFinalScore,
    AddressValidation_BestConfidence: addressValidationBestConfidence,
    AddressValidation_MatchedAddress: addressValidationMatchedAddress,
    TextValidation_Message: textValidationMessage,
    ImageValidation_Message: imageValidationMessage,
    emailValidation_Success: emailValidationSuccess || "FALSE",
    emailValidation_MatchedKeywords: emailValidationMatchedKeywords,
    HTMLCheck_FinalScore: htmlCheckResult?.finalScore || -1,
    HTMLCheck_MatchedKeywords:
      htmlCheckResult?.matchedKeywords?.join(" | ") || "NONE",
    SnippetCheck_FinalScore: snippetCheckResult?.finalScore || -1,
    SnippetCheck_MatchedKeywords:
      snippetCheckResult?.matchedKeywords?.join(" | ") || "NONE",
    FuzzyDomainCheck_FinalScore: fuzzyDomainResult?.finalScore || -1,
    FuzzyDomainCheck_MatchedKeywords:
      fuzzyDomainResult?.matchDetails?.matchedKeywords?.join(" | ") || "NONE",
    TextValidationResult: JSON.stringify(url.textValidation || []),
    ImageValidationResult: JSON.stringify(url.imageValidation || []),
    NumberValidationResult: JSON.stringify(url.numberValidation || []),
    AddressValidationResult: JSON.stringify(url.addressValidation || []),
    EmailValidationResult: JSON.stringify(url.emailValidation || []),
    promptCostText: promptTextCost,
    completionCostText: completionTextCost,
    totalTextCost: totalTextCost,
    promptCostImage: promptImageCost,
    completionCostImage: completionImageCost,
    totalImageCost: totalImageCost,
    totalGPTCost: totalTextCost + totalImageCost,
    ...urlMetadata,
  };
};

/**
 * Creates row data with only essential business information for CSV export
 * @param {Object} lead - The lead object
 * @param {Object|null} url - Optional URL object
 * @returns {Object} - Row data with essential business fields for CSV export
 */
const createMainRowData = (lead, url = null) => {
  const metadata = lead.metadata || {};

  // Include all metadata fields without filtering
  const essentialMetadata = { ...metadata };

  if (!url) {
    // For lead-level data (no URL)
    return {
      "Job ID": lead.jobId,
      "Seller Name": lead.sellerName,
      "Business Name": lead.businessName,
      Country: lead.country,
      Status: isLeadSuccessful(lead) ? "success" : "failure",
      "Seller Url": lead.sellerUrl,
      "Company Address": lead.address,
      ...essentialMetadata,
    };
  }

  // Process number validation
  let numberValidationSuccess = false;
  let numberValidationFinalScore = -1;
  let numberValidationMatchedKeywords = "NONE";

  if (Array.isArray(url.numberValidation)) {
    // New format (array)
    numberValidationSuccess = url.hasNumberMatch;
    // For backward compatibility still calculate these values
    numberValidationFinalScore = url.numberValidation.reduce((max, v) => {
      const score = v.result?.finalScore || 0;
      return score > max ? score : max;
    }, -1);
    // Collect all matched keywords
    const matchedKeywords = url.numberValidation.flatMap(
      (v) => v.result?.matchedKeywords || []
    );
    numberValidationMatchedKeywords = matchedKeywords.length
      ? matchedKeywords.join(" | ")
      : "NONE";
  } else {
    // Old format (object)
    const numberValidation = url.numberValidation || {};
    numberValidationSuccess = (Number(numberValidation?.finalScore) || 0) > 0;
    numberValidationFinalScore = numberValidation?.finalScore || -1;
    numberValidationMatchedKeywords =
      numberValidation?.matchedKeywords?.join(" | ") || "NONE";
  }

  // Process address validation
  let addressValidationFinalScore = -1;
  let addressValidationBestConfidence = -1;
  let addressValidationMatchedAddress = "NONE";

  if (Array.isArray(url.addressValidation)) {
    // New format (array)
    // Use hasAddressMatch flag directly
    addressValidationFinalScore = url.addressValidation.reduce((max, v) => {
      const score = v.result?.finalScore || 0;
      return score > max ? score : max;
    }, -1);
    addressValidationBestConfidence = url.addressValidation.reduce((max, v) => {
      const confidence = v.result?.bestConfidence || 0;
      return confidence > max ? confidence : max;
    }, -1);

    // Find the first address that matched
    const matchedAddressObj = url.addressValidation.find(
      (v) => v.result?.matchedAddress
    );
    addressValidationMatchedAddress =
      matchedAddressObj?.result?.matchedAddress || "NONE";
  } else {
    // Old format (object)
    const addressValidation = url.addressValidation || {};
    addressValidationFinalScore = addressValidation?.finalScore || -1;
    addressValidationBestConfidence = addressValidation?.bestConfidence || -1;
    addressValidationMatchedAddress =
      addressValidation?.matchedAddress || "NONE";
  }

  // Process text validation
  let textValidationMessage = "NONE";

  if (Array.isArray(url.textValidation)) {
    // New format (array) - use the hasTextMatch flag directly
    if (url.hasTextMatch) {
      textValidationMessage = "TRUE";
    } else {
      // Still check if any validation has a result
      const matchedText = url.textValidation.find(
        (v) => v.isMatch || v.result?.message?.toLowerCase() === "true"
      );
      const unmatchedText = url.textValidation.find(
        (v) => !v.isMatch && v.result?.message?.toLowerCase() === "false"
      );
      textValidationMessage = matchedText
        ? "TRUE"
        : unmatchedText
          ? "FALSE"
          : "NONE";
    }
  } else {
    // Old format (object)
    textValidationMessage = url.textValidation?.message || "NONE";
  }

  // Process image validation
  let imageValidationMessage = "NONE";

  if (Array.isArray(url.imageValidation)) {
    // New format (array) - use the hasImageMatch flag directly
    if (url.hasImageMatch) {
      imageValidationMessage = "TRUE";
    } else {
      // Still check if any validation has a positive result for backward compatibility
      const matchedImage = url.imageValidation.find(
        (v) => v.isMatch || v.result?.message?.toLowerCase() === "true"
      );
      const unmatchedImage = url.imageValidation.find(
        (v) => !v.isMatch && v.result?.message?.toLowerCase() === "false"
      );
      imageValidationMessage = matchedImage
        ? "TRUE"
        : unmatchedImage
          ? "FALSE"
          : "NONE";
    }
  } else {
    // Old format (object)
    imageValidationMessage =
      url.imageValidation?.message || url?.ImageValidation?.message || "NONE";
  }

  // Process email validation
  let emailValidationSuccess = false;
  let emailValidationMatchedKeywords = "NONE";

  if (Array.isArray(url.emailValidation)) {
    // New format (array) - use the hasEmailMatch flag directly
    emailValidationSuccess = url.hasEmailMatch;
    // Collect all matched keywords for backward compatibility
    const matchedKeywords = url.emailValidation.flatMap(
      (v) => v.result?.matchedKeywords || []
    );
    emailValidationMatchedKeywords = matchedKeywords.length
      ? matchedKeywords.join(" | ")
      : "NONE";
  } else {
    // Old format (object)
    const emailValidation = url.emailValidation || {};
    emailValidationSuccess = emailValidation?.finalScore > 0;
    emailValidationMatchedKeywords =
      emailValidation?.matchedKeywords?.join(" | ") || "NONE";
  }

  const htmlCheckResult = url.htmlCheckResult || {};
  const snippetCheckResult = url.snippetCheckResult || {};
  const fuzzyDomainResult = url.fuzzyDomainResult || {};

  // For URL-level data
  return {
    "Job ID": lead.jobId,
    "Seller Name": lead.sellerName,
    "Business Name": lead.businessName,
    Country: lead.country,
    "Search String": lead.searchString || "",
    "Confident Url": url.useDomain
      ? url.domain || ""
      : url.url || url.domain || "",
    Confidence: url.confidence,
    "Seller Url": lead.sellerUrl,
    "Company Address": lead.address,
    "Google Position": url.googlePosition,
    "Filter Domain Position": url.filterPosition,
    hasNumberMatch: url.hasNumberMatch || false,
    hasAddressMatch: url.hasAddressMatch || false,
    hasTextMatch: url.hasTextMatch || false,
    hasImageMatch: url.hasImageMatch || false,
    hasEmailMatch: url.hasEmailMatch || false,
    matchedTextUrls: JSON.stringify(url.matchedTextUrls || []),
    matchedNumberUrls: JSON.stringify(url.matchedNumberUrls || []),
    matchedAddressUrls: JSON.stringify(url.matchedAddressUrls || []),
    matchedEmailUrls: JSON.stringify(url.matchedEmailUrls || []),
    matchedImageUrls: JSON.stringify(url.matchedImageUrls || []),
    numberValidation_Success: numberValidationSuccess || "FALSE",
    numberValidation_FinalScore: numberValidationFinalScore,
    numberValidation_MatchedKeywords: numberValidationMatchedKeywords,
    AddressValidation_FinalScore: addressValidationFinalScore,
    AddressValidation_BestConfidence: addressValidationBestConfidence,
    AddressValidation_MatchedAddress: addressValidationMatchedAddress,
    TextValidation_Message: textValidationMessage,
    ImageValidation_Message: imageValidationMessage,
    emailValidation_Success: emailValidationSuccess || "FALSE",
    emailValidation_MatchedKeywords: emailValidationMatchedKeywords,
    HTMLCheck_FinalScore: htmlCheckResult?.finalScore || -1,
    HTMLCheck_MatchedKeywords:
      htmlCheckResult?.matchedKeywords?.join(" | ") || "NONE",
    SnippetCheck_FinalScore: snippetCheckResult?.finalScore || -1,
    SnippetCheck_MatchedKeywords:
      snippetCheckResult?.matchedKeywords?.join(" | ") || "NONE",
    FuzzyDomainCheck_FinalScore: fuzzyDomainResult?.finalScore || -1,
    FuzzyDomainCheck_MatchedKeywords:
      fuzzyDomainResult?.matchDetails?.matchedKeywords?.join(" | ") || "NONE",
    TextValidationResult: JSON.stringify(url.textValidation || []),
    ImageValidationResult: JSON.stringify(url.imageValidation || []),
    NumberValidationResult: JSON.stringify(url.numberValidation || []),
    AddressValidationResult: JSON.stringify(url.addressValidation || []),
    EmailValidationResult: JSON.stringify(url.emailValidation || []),
    ...essentialMetadata,
  };
};

/**
 * Creates row data with all technical debugging info for CSV export
 * @param {Object} lead - The lead object
 * @param {Object|null} url - Optional URL object
 * @param {Array} counter - Array containing [urlIndex, hasSuccessfulUrl]
 * @returns {Object} - Row data with all debugging information for CSV export
 */
const createDebugRowData = (lead, url = null, counter = [false]) => {
  // Use the existing createRowData implementation for debug data
  return createRowData(lead, url, counter);
};

/**
 * Prepares export data for a given job
 * @param {Array} jobIds - Array of job IDs to export
 * @returns {Promise<Object>} - Export data and metadata
 */
const prepareExportData = async (jobIds) => {
  console.log(
    `[Export] Starting data preparation for jobs: ${jobIds.join(", ")}`
  );
  const startTime = Date.now();

  // First fetch just the leads without URLs
  const batchSize = 1000;
  let skip = 0;
  let allLeads = [];
  let leadBatchCount = 0;

  console.log(`[Export] Fetching leads in batches of ${batchSize}...`);

  while (true) {
    leadBatchCount++;
    console.log(
      `[Export] Fetching leads batch #${leadBatchCount} (skip: ${skip})...`
    );

    const leads = await prisma.lead.findMany({
      where: { jobId: { in: jobIds } },
      take: batchSize,
      skip: skip,
    });

    if (leads.length === 0) break;
    allLeads = allLeads.concat(leads);
    skip += batchSize;

    console.log(
      `[Export] Batch #${leadBatchCount} complete. Total leads so far: ${allLeads.length}`
    );
  }

  const leadsTime = Date.now();
  console.log(
    `[Export] Completed leads fetch in ${(leadsTime - startTime) / 1000}s. Total leads: ${allLeads.length}`
  );

  // Then fetch URLs separately in batches for each lead
  const urlBatchSize = 1000;
  const processedLeads = [];
  let urlBatchCount = 0;
  let totalUrls = 0;

  console.log(
    `[Export] Starting URL fetch in batches of ${urlBatchSize} leads...`
  );

  for (let i = 0; i < allLeads.length; i += urlBatchSize) {
    urlBatchCount++;
    const batchStartTime = Date.now();

    const leadBatch = allLeads.slice(i, i + urlBatchSize);
    const leadIds = leadBatch.map((lead) => lead.id);

    console.log(
      `[Export] Processing URL batch #${urlBatchCount} for leads ${i + 1}-${Math.min(i + urlBatchSize, allLeads.length)}...`
    );

    const urls = await prisma.leadUrl.findMany({
      where: { leadId: { in: leadIds } },
      orderBy: {
        googlePosition: "asc",
      },
    });

    totalUrls += urls.length;
    console.log(`[Export] Batch #${urlBatchCount} fetched ${urls.length} URLs`);

    // Group URLs by leadId
    const urlsByLead = urls.reduce((acc, url) => {
      if (!acc[url.leadId]) {
        acc[url.leadId] = [];
      }
      acc[url.leadId].push(url);
      return acc;
    }, {});

    // Combine leads with their URLs
    const processedBatch = leadBatch.map((lead) => ({
      ...lead,
      urls: urlsByLead[lead.id] || [],
    }));

    processedLeads.push(...processedBatch);

    const batchTime = (Date.now() - batchStartTime) / 1000;
    console.log(`[Export] Batch #${urlBatchCount} processed in ${batchTime}s`);
  }

  const urlsTime = Date.now();
  console.log(
    `[Export] Completed URL fetch in ${(urlsTime - leadsTime) / 1000}s. Total URLs: ${totalUrls}`
  );
  console.log(`[Export] Starting data transformation...`);

  // Process main data (simplified)
  const mainData = processedLeads.map((lead) => createMainRowData(lead));

  // Process URL level data (simplified - top 3 URLs per lead)
  const mainUrlLevelData = [];
  for (const lead of processedLeads) {
    const urls = lead.urls.slice(0, 3);
    for (const url of urls) {
      mainUrlLevelData.push(createMainRowData(lead, url));
    }
  }

  // Process debug data (all details)
  const debugData = processedLeads.map((lead) => createDebugRowData(lead));

  // Process URL level debug data (all details - top 3 URLs per lead)
  const debugUrlLevelData = [];
  for (const lead of processedLeads) {
    const urls = lead.urls.slice(0, 3);
    for (const url of urls) {
      debugUrlLevelData.push(createDebugRowData(lead, url));
    }
  }

  // Process validated data (using simplified main data format)
  const validatedData = [];
  for (const lead of processedLeads) {
    const urls = lead.urls;
    for (const url of urls) {
      if (isValidUrl(url)) {
        validatedData.push(createMainRowData(lead, url));
      }
    }
  }

  // Process confidence filtered data (using simplified main data format)
  const confidenceData = [];
  for (const lead of processedLeads) {
    const urls = lead.urls;
    for (const url of urls) {
      if (isUrlSuccessful(url)) {
        confidenceData.push(createMainRowData(lead, url));
      }
    }
  }

  const endTime = Date.now();
  const totalTime = (endTime - startTime) / 1000;

  console.log(`[Export] Data preparation complete:`);
  console.log(`- Total time: ${totalTime}s`);
  console.log(`- Total leads: ${mainData.length}`);
  console.log(`- Total URLs: ${totalUrls}`);
  console.log(`- Valid URLs: ${validatedData.length}`);
  console.log(`- Confidence filtered URLs: ${confidenceData.length}`);

  return {
    mainData,
    mainUrlLevelData,
    debugData,
    debugUrlLevelData,
    validatedData,
    confidenceData,
  };
};

/**
 * Creates CSV files for export
 * @param {Object} data - Export data
 * @param {Array} jobIds - Array of job IDs
 * @param {Boolean} includeDebug - Whether to include debug data sheets
 * @returns {Promise<Object>} - Files and metadata
 */
const createExportFiles = async (data, jobIds, includeDebug = true) => {
  const {
    mainData,
    mainUrlLevelData,
    debugData,
    debugUrlLevelData,
    validatedData,
    confidenceData,
  } = data;

  const jobIdsString = jobIds.join("-");
  const exportDir = path.join(__dirname, "../exports");
  if (!fs.existsSync(exportDir)) {
    fs.mkdirSync(exportDir, { recursive: true });
  }

  const createCsvFile = async (data, filename) => {
    const csvContent = await converter.json2csv(data);
    const filePath = path.join(exportDir, filename);
    fs.writeFileSync(filePath, csvContent);
    return { filename, content: csvContent, path: filePath };
  };

  // Create main data files
  let filesPromises = [
    // Main data files
    createCsvFile(mainData, `jobs-${jobIdsString}-main.csv`),
    createCsvFile(mainUrlLevelData, `jobs-${jobIdsString}-url-level-data.csv`),
    createCsvFile(validatedData, `jobs-${jobIdsString}-validated.csv`),
    createCsvFile(confidenceData, `jobs-${jobIdsString}-confidence.csv`),
  ];

  // Only include debug files if includeDebug is true
  if (includeDebug) {
    filesPromises = filesPromises.concat([
      createCsvFile(debugData, `jobs-${jobIdsString}-main-debug.csv`),
      createCsvFile(
        debugUrlLevelData,
        `jobs-${jobIdsString}-url-level-data-debug.csv`
      ),
      createCsvFile(validatedData, `jobs-${jobIdsString}-validated-debug.csv`),
      createCsvFile(
        confidenceData,
        `jobs-${jobIdsString}-confidence-debug.csv`
      ),
    ]);
  }

  const files = await Promise.all(filesPromises);

  // Create appropriate sheet names based on includeDebug flag
  const sheetNames = {
    main: "Main Data",
    urlLevel: "URL Level Data",
    validated: "Validated URLs",
    confidence: "High Confidence URLs",
  };

  // Only add debug sheet names if includeDebug is true
  if (includeDebug) {
    sheetNames.mainDebug = "Main Data (Debug)";
    sheetNames.urlLevelDebug = "URL Level Data (Debug)";
    sheetNames.validatedDebug = "Validated URLs (Debug)";
    sheetNames.confidenceDebug = "High Confidence URLs (Debug)";
  }

  return { files, sheetNames };
};

/**
 * Creates a Google Sheet from CSV files
 * @param {Object} exportFiles - Files and metadata from createExportFiles
 * @param {Array} jobIds - Array of job IDs
 * @param {Boolean} sendSheet - Whether to send data to Google Sheets
 * @returns {Promise<Object>} - Google Sheet information
 */
const createGoogleSheet = async (exportFiles, jobIds, sendSheet = false) => {
  if (!sendSheet) {
    return {
      googleSheet: null,
      sheetNames: null,
      multiSheetTitle: null,
    };
  }
  const { files, sheetNames } = exportFiles;

  // Create a single Google Sheet with multiple sheets
  const csvFilesForSheet = files.map((file) => ({
    path: file.path,
    name: path.basename(file.filename, ".csv"),
  }));

  console.log(
    "CSV files for sheets:",
    csvFilesForSheet.map((f) => f.name)
  );

  const jobIdsString = jobIds.join("-");
  const multiSheetTitle = `Jobs ${jobIdsString} Export - ${new Date().toLocaleString()}`;

  // Add debugging to trace the inputs and outputs of the sheet creation
  console.log("Creating Google Sheet with title:", multiSheetTitle);
  console.log(
    "Input files:",
    files.map((f) => f.filename)
  );

  const googleSheet = await createMultiSheetFromCsvs(
    csvFilesForSheet,
    multiSheetTitle
  );

  console.log("Google Sheet URL:", googleSheet.sheetUrl);

  console.log(
    "Created sheets:",
    googleSheet.sheets.map((s) => s.name)
  );

  // Ensure all expected sheets are present
  const expectedSheets = Object.values(sheetNames);

  // Check if any expected sheets are missing
  const missingSheets = expectedSheets.filter(
    (sheet) => !googleSheet.sheets.some((s) => s.name === sheet)
  );

  if (missingSheets.length > 0) {
    console.warn("Warning: Some expected sheets are missing:", missingSheets);
  }

  // Clean up temporary CSV files
  files.forEach(async (file) => {
    try {
      await fs.promises.unlink(file.path);
    } catch (err) {
      console.error(`Error deleting file ${file.path}:`, err);
    }
  });

  return { googleSheet, sheetNames, multiSheetTitle };
};

/**
 * Creates a ZIP file from CSV files
 * @param {Object} exportFiles - Files and metadata from createExportFiles
 * @param {Array} jobIds - Array of job IDs
 * @param {Object} googleSheetInfo - Google Sheet info from createGoogleSheet
 * @returns {Promise<Object>} - ZIP file information
 */
const createZipFile = async (exportFiles, jobIds, googleSheetInfo) => {
  const { files } = exportFiles;
  const { googleSheet } = googleSheetInfo;

  const jobIdsString = jobIds.join("-");
  const zipFileName = `jobs-${jobIdsString}-export.zip`;
  const exportDir = path.join(__dirname, "../exports");
  const zipFilePath = path.join(exportDir, zipFileName);
  const zip = new require("adm-zip")();

  files.forEach((file) => {
    zip.addFile(file.filename, Buffer.from(file.content));
  });

  // Add a file with Google Sheet link
  const sheetInfoContent = `Combined Google Sheet: ${googleSheet?.sheetUrl}\n\nSheet Names:\n${googleSheet?.sheets
    ?.map((sheet) => `- ${sheet.name}`)
    ?.join("\n")}`;
  zip.addFile("google_sheet_link.txt", Buffer.from(sheetInfoContent));

  zip.writeZip(zipFilePath);

  return { zipFileName, zipFilePath };
};

/**
 * Prepares the response object for the export
 * @param {Object} exportData - Data from prepareExportData
 * @param {Object} googleSheetInfo - Google Sheet info from createGoogleSheet
 * @param {Boolean} includeDebug - Whether debug sheets were included
 * @returns {Object} - Response object
 */
const prepareExportResponse = (
  exportData,
  googleSheetInfo,
  includeDebug = false
) => {
  const {
    mainData,
    mainUrlLevelData,
    debugData,
    debugUrlLevelData,
    validatedData,
    confidenceData,
  } = exportData;

  const { googleSheet, sheetNames } = googleSheetInfo;

  return {
    success: true,
    message: "Export links generated successfully",
    googleSheet: {
      name: googleSheet?.spreadsheetTitle || googleSheet?.spreadsheetId,
      url: googleSheet?.sheetUrl,
      sheets: googleSheet?.sheets?.map((sheet) => ({
        name: sheet.name,
        id: sheet.sheetId,
        rows: sheet.name.includes("validated")
          ? validatedData.length
          : sheet.name.includes("confidence")
            ? confidenceData.length
            : sheet.name.includes("url-level")
              ? mainUrlLevelData.length
              : sheet.name.includes("main")
                ? mainData.length
                : 0,
      })),
    },
    stats: {
      totalLeads: mainData.length,
      totalUrls: mainUrlLevelData.length,
      validatedUrls: validatedData.length,
      confidenceFilteredUrls: confidenceData.length,
      includeDebug: includeDebug,
    },
  };
};

/**
 * Main function to generate Google Sheet for jobs
 * @param {Array} jobIds - Array of job IDs to export
 * @param {Boolean} sendSheet - Whether to send the data to Google Sheets
 * @param {Boolean} includeDebug - Whether to include debug data
 * @returns {Promise<Object>} - Export information including Google Sheet URL
 */
const generateJobExport = async (
  jobIds,
  sendSheet = false,
  includeDebug = false
) => {
  try {
    // Step 1: Prepare the export data from the database
    const exportData = await prepareExportData(jobIds);

    // Step 2: Create CSV files for export
    const exportFiles = await createExportFiles(
      exportData,
      jobIds,
      includeDebug
    );

    const googleSheetInfo = await createGoogleSheet(
      exportFiles,
      jobIds,
      sendSheet
    );

    // Step 4: Prepare response
    const response = prepareExportResponse(
      exportData,
      googleSheetInfo,
      includeDebug
    );

    return response;
  } catch (error) {
    console.error("Error generating job export:", error);
    throw error;
  }
};

/**
 * Generates an export with downloadable ZIP file
 * @param {Array} jobIds - Array of job IDs to export
 * @param {Boolean} sendSheet - Whether to send the data to Google Sheets
 * @param {Boolean} includeDebug - Whether to include debug data
 * @returns {Promise<Object>} - Export information including ZIP file path
 */
const generateJobExportWithZip = async (
  jobIds,
  sendSheet = false,
  includeDebug = false
) => {
  try {
    // Step 1: Prepare the export data from the database
    const exportData = await prepareExportData(jobIds);

    // Step 2: Create CSV files for export
    const exportFiles = await createExportFiles(
      exportData,
      jobIds,
      includeDebug
    );

    // Step 3: Create Google Sheet
    const googleSheetInfo = await createGoogleSheet(
      exportFiles,
      jobIds,
      sendSheet
    );

    // Step 4: Create ZIP file
    const zipInfo = await createZipFile(exportFiles, jobIds, googleSheetInfo);

    // Step 5: Prepare response
    const response = prepareExportResponse(
      exportData,
      googleSheetInfo,
      includeDebug
    );
    response.zipFile = zipInfo;

    return response;
  } catch (error) {
    console.error("Error generating job export with ZIP:", error);
    throw error;
  }
};

/**
 * Creates a temporary directory for export files
 * @returns {string} - Path to temporary directory
 */
const createTempDir = () => {
  const tempDir = path.join(os.tmpdir(), `lead-export-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }
  return tempDir;
};

/**
 * Streams CSV data to file to avoid memory issues
 * @param {Array} headers - CSV headers
 * @param {string} filePath - Output file path
 * @returns {Object} - CSV writer instance
 */
const createStreamingCsvWriter = (headers, filePath) => {
  return createCsvWriter({
    path: filePath,
    header: headers
  });
};

/**
 * Transforms row data to match CSV headers for streaming
 * @param {Object} rowData - Row data from createMainRowData
 * @param {Boolean} includeDebug - Whether debug data is included
 * @param {Boolean} isUrlLevel - Whether this is URL-level data
 * @returns {Object} - Transformed row data
 */
const transformRowForStreaming = (rowData, includeDebug, isUrlLevel = false) => {
  const transformed = {
    jobId: rowData["Job ID"],
    sellerName: rowData["Seller Name"],
    businessName: rowData["Business Name"],
    country: rowData.Country,
    status: rowData.Status,
    sellerUrl: rowData["Seller Url"],
    address: rowData["Company Address"]
  };

  if (includeDebug && rowData["Raw SERP Output"]) {
    transformed.rawSerpOutput = rowData["Raw SERP Output"];
  }

  if (isUrlLevel) {
    transformed.searchString = rowData["Search String"];
    transformed.confidentUrl = rowData["Confident Url"];
    transformed.confidence = rowData.Confidence;
    transformed.googlePosition = rowData["Google Position"];
    transformed.filterPosition = rowData["Filter Domain Position"];
    transformed.hasNumberMatch = rowData.hasNumberMatch;
    transformed.hasAddressMatch = rowData.hasAddressMatch;
    transformed.hasTextMatch = rowData.hasTextMatch;
    transformed.hasImageMatch = rowData.hasImageMatch;
    transformed.hasEmailMatch = rowData.hasEmailMatch;
  }

  return transformed;
};

/**
 * Processes leads in batches and streams to CSV files
 * @param {Array} jobIds - Array of job IDs to export
 * @param {Boolean} includeDebug - Whether to include debug information
 * @param {string} tempDir - Temporary directory path
 * @returns {Promise<Object>} - Export file information
 */
const createStreamingExportFiles = async (jobIds, includeDebug = false, tempDir) => {
  const BATCH_SIZE = 50; // Process 50 leads at a time for better memory management
  const jobIdsString = jobIds.join("-");

  console.log(`[Streaming Export] Starting streaming export for jobs: ${jobIds.join(", ")}`);

  // Define CSV headers for different file types
  const mainHeaders = [
    { id: "jobId", title: "Job ID" },
    { id: "sellerName", title: "Seller Name" },
    { id: "businessName", title: "Business Name" },
    { id: "country", title: "Country" },
    { id: "status", title: "Status" },
    { id: "sellerUrl", title: "Seller Url" },
    { id: "address", title: "Company Address" }
  ];

  if (includeDebug) {
    mainHeaders.push({ id: "rawSerpOutput", title: "Raw SERP Output" });
  }

  // Create file paths
  const mainFilePath = path.join(tempDir, `jobs-${jobIdsString}-main.csv`);
  const urlLevelFilePath = path.join(tempDir, `jobs-${jobIdsString}-url-level-data.csv`);
  const validatedFilePath = path.join(tempDir, `jobs-${jobIdsString}-validated.csv`);
  const confidenceFilePath = path.join(tempDir, `jobs-${jobIdsString}-confidence.csv`);

  // Create CSV writers
  const mainWriter = createStreamingCsvWriter(mainHeaders, mainFilePath);

  // Extended headers for URL-level data
  const urlHeaders = [
    ...mainHeaders,
    { id: "searchString", title: "Search String" },
    { id: "confidentUrl", title: "Confident Url" },
    { id: "confidence", title: "Confidence" },
    { id: "googlePosition", title: "Google Position" },
    { id: "filterPosition", title: "Filter Domain Position" },
    { id: "hasNumberMatch", title: "hasNumberMatch" },
    { id: "hasAddressMatch", title: "hasAddressMatch" },
    { id: "hasTextMatch", title: "hasTextMatch" },
    { id: "hasImageMatch", title: "hasImageMatch" },
    { id: "hasEmailMatch", title: "hasEmailMatch" }
  ];

  const urlWriter = createStreamingCsvWriter(urlHeaders, urlLevelFilePath);
  const validatedWriter = createStreamingCsvWriter(urlHeaders, validatedFilePath);
  const confidenceWriter = createStreamingCsvWriter(urlHeaders, confidenceFilePath);

  let totalLeads = 0;
  let processedLeads = 0;
  let skip = 0;

  // First, get total count for progress tracking
  totalLeads = await prisma.lead.count({
    where: { jobId: { in: jobIds } }
  });

  console.log(`[Streaming Export] Total leads to process: ${totalLeads}`);

  // Process leads in batches
  while (true) {
    const leads = await prisma.lead.findMany({
      where: { jobId: { in: jobIds } },
      include: {
        urls: {
          take: 3, // Limit to top 3 URLs per lead
          orderBy: { confidence: 'desc' }
        },
        job: true
      },
      take: BATCH_SIZE,
      skip: skip,
      orderBy: { id: 'asc' }
    });

    if (leads.length === 0) break;

    // Process this batch
    const mainBatch = [];
    const urlBatch = [];
    const validatedBatch = [];
    const confidenceBatch = [];

    for (const lead of leads) {
      // Create main row data
      const mainRowData = createMainRowData(lead, null, includeDebug);
      const mainRow = transformRowForStreaming(mainRowData, includeDebug, false);
      mainBatch.push(mainRow);

      // Process URLs for this lead
      for (const url of lead.urls) {
        const urlRowData = createMainRowData(lead, url, includeDebug);
        const urlRow = transformRowForStreaming(urlRowData, includeDebug, true);
        urlBatch.push(urlRow);

        // Add to validated if valid
        if (isValidUrl(url)) {
          validatedBatch.push(urlRow);
        }

        // Add to confidence if successful
        if (isUrlSuccessful(url)) {
          confidenceBatch.push(urlRow);
        }
      }
    }

    // Write batches to files
    if (mainBatch.length > 0) await mainWriter.writeRecords(mainBatch);
    if (urlBatch.length > 0) await urlWriter.writeRecords(urlBatch);
    if (validatedBatch.length > 0) await validatedWriter.writeRecords(validatedBatch);
    if (confidenceBatch.length > 0) await confidenceWriter.writeRecords(confidenceBatch);

    processedLeads += leads.length;
    skip += BATCH_SIZE;

    const percentage = Math.round(processedLeads/totalLeads*100);
    console.log(`[Streaming Export] Processed ${processedLeads}/${totalLeads} leads (${percentage}%)`);

    // Force garbage collection hint for large datasets
    if (processedLeads % 500 === 0 && global.gc) {
      global.gc();
    }
  }

  console.log(`[Streaming Export] Completed processing ${processedLeads} leads`);

  return {
    mainFile: { filename: `jobs-${jobIdsString}-main.csv`, path: mainFilePath },
    urlLevelFile: { filename: `jobs-${jobIdsString}-url-level-data.csv`, path: urlLevelFilePath },
    validatedFile: { filename: `jobs-${jobIdsString}-validated.csv`, path: validatedFilePath },
    confidenceFile: { filename: `jobs-${jobIdsString}-confidence.csv`, path: confidenceFilePath },
    tempDir
  };
};

/**
 * Creates a ZIP file from streaming export files
 * @param {Object} exportFiles - Export file information
 * @param {Array} jobIds - Array of job IDs
 * @returns {Promise<Object>} - ZIP file information
 */
const createStreamingZipFile = async (exportFiles, jobIds) => {
  const jobIdsString = jobIds.join("-");
  const zipFileName = `jobs-${jobIdsString}-export.zip`;
  const zipFilePath = path.join(exportFiles.tempDir, zipFileName);

  return new Promise((resolve, reject) => {
    const output = fs.createWriteStream(zipFilePath);
    const archive = archiver('zip', {
      zlib: { level: 6 }, // Balanced compression (faster than 9, still good compression)
      store: false // Ensure compression is enabled
    });

    output.on('close', () => {
      console.log(`[Streaming Export] ZIP file created: ${archive.pointer()} total bytes`);
      resolve({
        zipFileName,
        zipFilePath,
        size: archive.pointer()
      });
    });

    archive.on('error', (err) => {
      console.error('[Streaming Export] ZIP creation error:', err);
      reject(err);
    });

    archive.pipe(output);

    // Add all CSV files to the ZIP
    const filesToAdd = [
      exportFiles.mainFile,
      exportFiles.urlLevelFile,
      exportFiles.validatedFile,
      exportFiles.confidenceFile
    ];

    for (const file of filesToAdd) {
      if (fs.existsSync(file.path)) {
        archive.file(file.path, { name: file.filename });
      }
    }

    archive.finalize();
  });
};

/**
 * Memory-optimized export function using streaming
 * @param {Array} jobIds - Array of job IDs to export
 * @param {Boolean} includeDebug - Whether to include debug data
 * @returns {Promise<Object>} - Export information including ZIP file path
 */
const generateJobExportWithStreamingZip = async (jobIds, includeDebug = false) => {
  let tempDir = null;

  try {
    console.log(`[Streaming Export] Starting memory-optimized export for jobs: ${jobIds.join(", ")}`);

    // Create temporary directory
    tempDir = createTempDir();

    // Create streaming export files
    const exportFiles = await createStreamingExportFiles(jobIds, includeDebug, tempDir);

    // Create ZIP file
    const zipInfo = await createStreamingZipFile(exportFiles, jobIds);

    console.log(`[Streaming Export] Export completed successfully`);

    return {
      success: true,
      zipFile: zipInfo,
      tempDir: tempDir,
      message: "Export completed using memory-optimized streaming"
    };

  } catch (error) {
    console.error("Error in streaming export:", error);

    // Cleanup on error
    if (tempDir && fs.existsSync(tempDir)) {
      try {
        fs.rmSync(tempDir, { recursive: true, force: true });
      } catch (cleanupError) {
        console.error("Error cleaning up temp directory:", cleanupError);
      }
    }

    throw error;
  }
};

module.exports = {
  isValidUrl,
  isUrlSuccessful,
  isLeadSuccessful,
  getTotalTokens,
  createRowData,
  createMainRowData,
  createDebugRowData,
  prepareExportData,
  createExportFiles,
  createGoogleSheet,
  createZipFile,
  prepareExportResponse,
  generateJobExport,
  generateJobExportWithZip,
  createTempDir,
  createStreamingCsvWriter,
  transformRowForStreaming,
  createStreamingExportFiles,
  createStreamingZipFile,
  generateJobExportWithStreamingZip,
};
