/**
 * Azure App Service startup configuration
 * This file handles Azure-specific configurations and health check setup
 */

const express = require("express");
const cors = require("cors");

require("dotenv").config();

// Azure-specific environment variables
const PORT = process.env.PORT || process.env.WEBSITES_PORT || 8000;
const isAzure = process.env.WEBSITE_SITE_NAME ? true : false;

console.log(`🚀 Starting application...`);
console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`🌐 Port: ${PORT}`);
console.log(`☁️ Azure App Service: ${isAzure ? 'Yes' : 'No'}`);

if (isAzure) {
  console.log(`🏷️ Azure Site Name: ${process.env.WEBSITE_SITE_NAME}`);
  console.log(`🔧 Azure Resource Group: ${process.env.WEBSITE_RESOURCE_GROUP || 'Not set'}`);
}

// Export configuration for use in main application
module.exports = {
  PORT,
  isAzure,
  azureConfig: {
    siteName: process.env.WEBSITE_SITE_NAME,
    resourceGroup: process.env.WEBSITE_RESOURCE_GROUP,
    subscriptionId: process.env.WEBSITE_OWNER_NAME,
    instanceId: process.env.WEBSITE_INSTANCE_ID,
    hostname: process.env.WEBSITE_HOSTNAME
  }
};
