# Azure App Service Deployment Guide

This guide covers deploying the SellerBot API to Azure App Service with comprehensive health check monitoring.

## Health Check Endpoints

The application provides multiple health check endpoints for Azure App Service monitoring:

### 1. Main Health Check - `/health`
**Recommended for Azure App Service health monitoring**

```bash
GET https://your-app.azurewebsites.net/health
```

**Features:**
- Comprehensive system health verification
- Database connectivity check
- File system access verification
- Memory usage monitoring
- Environment configuration validation
- Returns HTTP 200 (healthy) or 503 (unhealthy)

**Response Example:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600,
  "version": "1.0.0",
  "environment": "production",
  "checks": {
    "database": {
      "status": "healthy",
      "responseTime": 45
    },
    "filesystem": {
      "status": "healthy",
      "tempDir": true,
      "writable": true,
      "readable": true
    },
    "memory": {
      "status": "healthy",
      "usage": {
        "rss": 256,
        "heapUsed": 128,
        "systemPercentage": 25
      }
    }
  },
  "responseTime": 67
}
```

### 2. Simple Health Check - `/health/simple`
**For load balancers requiring fast responses**

```bash
GET https://your-app.azurewebsites.net/health/simple
```

### 3. Readiness Probe - `/health/ready`
**Kubernetes-style readiness check**

```bash
GET https://your-app.azurewebsites.net/health/ready
```

### 4. Liveness Probe - `/health/live`
**Kubernetes-style liveness check**

```bash
GET https://your-app.azurewebsites.net/health/live
```

## Azure App Service Configuration

### 1. Application Settings

Configure these environment variables in Azure App Service:

```bash
# Required
DATABASE_URL=postgresql://username:password@host:port/database
NODE_ENV=production

# Optional Azure-specific
WEBSITES_ENABLE_APP_SERVICE_STORAGE=true
WEBSITES_PORT=8000
```

### 2. Health Check Configuration

In Azure Portal:
1. Go to your App Service
2. Navigate to **Monitoring** > **Health check**
3. Enable health check
4. Set health check path: `/health`
5. Configure:
   - **Interval**: 30 seconds
   - **Timeout**: 10 seconds
   - **Unhealthy threshold**: 3 consecutive failures

### 3. Application Insights Integration

The health check endpoint provides detailed metrics that integrate with Azure Application Insights:

- Response times
- Error rates
- Memory usage
- Database performance
- Custom health metrics

## Deployment Steps

### Method 1: GitHub Actions (Recommended)

1. **Set up GitHub repository secrets:**
   ```
   AZURE_WEBAPP_PUBLISH_PROFILE
   ```

2. **Create workflow file** (`.github/workflows/azure-deploy.yml`):
   ```yaml
   name: Deploy to Azure App Service
   
   on:
     push:
       branches: [ main ]
   
   jobs:
     deploy:
       runs-on: ubuntu-latest
       steps:
       - uses: actions/checkout@v2
       
       - name: Setup Node.js
         uses: actions/setup-node@v2
         with:
           node-version: '18'
           
       - name: Install dependencies
         run: npm install
         
       - name: Generate documentation
         run: npm run generate-docs
         
       - name: Deploy to Azure
         uses: azure/webapps-deploy@v2
         with:
           app-name: 'your-app-name'
           publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
   ```

### Method 2: Azure CLI

```bash
# Login to Azure
az login

# Create resource group
az group create --name sellerbot-rg --location "East US"

# Create App Service plan
az appservice plan create --name sellerbot-plan --resource-group sellerbot-rg --sku B1 --is-linux

# Create web app
az webapp create --resource-group sellerbot-rg --plan sellerbot-plan --name your-app-name --runtime "NODE|18-lts"

# Configure app settings
az webapp config appsettings set --resource-group sellerbot-rg --name your-app-name --settings NODE_ENV=production DATABASE_URL="your-connection-string"

# Deploy from local Git
az webapp deployment source config-local-git --name your-app-name --resource-group sellerbot-rg

# Add Azure remote and deploy
git remote add azure https://your-app-name.scm.azurewebsites.net:443/your-app-name.git
git push azure main
```

### Method 3: VS Code Extension

1. Install Azure App Service extension
2. Sign in to Azure
3. Right-click on your project
4. Select "Deploy to Web App"
5. Follow the prompts

## Monitoring and Troubleshooting

### Health Check Monitoring

Monitor your application health using:

```bash
# Check main health endpoint
curl https://your-app.azurewebsites.net/health

# Check simple health
curl https://your-app.azurewebsites.net/health/simple

# Monitor with watch (Linux/Mac)
watch -n 30 'curl -s https://your-app.azurewebsites.net/health | jq .status'
```

### Azure Portal Monitoring

1. **App Service Logs**: Monitor application logs in real-time
2. **Metrics**: View CPU, memory, and request metrics
3. **Application Insights**: Detailed performance monitoring
4. **Health Check**: Automated health monitoring with alerts

### Common Issues

**Database Connection Issues:**
- Verify DATABASE_URL environment variable
- Check firewall rules for Azure Database
- Ensure connection string format is correct

**Memory Issues:**
- Monitor `/health` endpoint for memory usage
- Scale up App Service plan if needed
- Check for memory leaks in application logs

**File System Issues:**
- Verify temp directory permissions
- Check disk space usage
- Review file upload configurations

## Performance Optimization

### App Service Configuration

```bash
# Enable Always On (prevents cold starts)
az webapp config set --resource-group sellerbot-rg --name your-app-name --always-on true

# Configure auto-scaling
az monitor autoscale create --resource-group sellerbot-rg --resource your-app-name --resource-type Microsoft.Web/serverfarms --name autoscale-rules --min-count 1 --max-count 5 --count 2
```

### Health Check Optimization

The health check endpoints are optimized for performance:
- `/health/simple` - Fastest response (~1ms)
- `/health/live` - Basic liveness check (~2ms)
- `/health/ready` - Database connectivity check (~50ms)
- `/health` - Comprehensive check (~100ms)

Choose the appropriate endpoint based on your monitoring requirements.

## Security Considerations

1. **Environment Variables**: Store sensitive data in App Service application settings
2. **HTTPS**: Enable HTTPS-only in App Service configuration
3. **Authentication**: Configure authentication if needed
4. **Network Security**: Use VNet integration for database connections
5. **Health Check Security**: Health endpoints are public but don't expose sensitive data

## Support

For deployment issues:
1. Check Azure App Service logs
2. Monitor health check endpoints
3. Review Application Insights metrics
4. Check environment variable configuration
