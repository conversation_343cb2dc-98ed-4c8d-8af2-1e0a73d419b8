/**
 * Funnel configuration for SellerBot
 * This file defines all supported funnels, their API endpoints, and required fields
 */

const { companySchemaMap, prospectSchemaMap, amazonSellerSchemaMap } = require('../../middlewares/schema');

/**
 * Configuration for all available funnels
 * 
 * Each funnel has the following properties:
 * - apiEndpoint: The API endpoint to use (required)
 * - mode: Processing mode for data validation (required)
 * - requiredFields: Map of required fields and their schema mappings (required)
 * - optionalFields: Map of optional fields and their schema mappings (optional)
 * - updatedApiRequiredFields: Map of required fields when using new API endpoints (optional)
 * - lookupSourceRules: Rules for setting lookup_source based on conditions (For UpdateCompanies mode)
 *   - Can be a direct mapping from website_status to lookupSource: {'Final Correct': 'SOME_SOURCE'}
 *   - A special condition using 'EXCEPT': {'EXCEPT:Final Correct': 'SOME_SOURCE'} - use this source for anything except 'Final Correct'
 *   - A default fallback using 'DEFAULT:': {'DEFAULT:': 'SOME_SOURCE'} - use this source when no other rules match
 * - sourceValue: Single source value to use (For UpdateProspects mode)
 * - subFunnels: Configuration for sub-funnels (optional)
 */
const funnelConfig = {
  // Website Search funnels
  'Website Search (Apollo)': {
    apiEndpoint: 'UpdateCompanies',
    subFunnels: {
      'Record Pass': {
        mode: 'CompanyUpdate',
        requiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
        },
        // Additional fields required when using new API endpoints
        updatedApiRequiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          seller_url: amazonSellerSchemaMap.seller_url,
        },
        lookupSourceRules: {
          'DEFAULT:': 'WEBSITE_SEARCH_APOLLO_P1'
        }
      },
      'Record Fail': {
        mode: 'CompanyUpdate',
        requiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
        },
        updatedApiRequiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          seller_url: amazonSellerSchemaMap.seller_url,
        },
        lookupSourceRules: {
          'DEFAULT:': 'WEBSITE_SEARCH_APOLLO_P1:fail'
        }
      },
      'Record Success/Fail': {
        mode: 'CompanyUpdate',
        requiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          website_status: companySchemaMap.website_status,
          website: companySchemaMap.website,
        },
        updatedApiRequiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          website_status: companySchemaMap.website_status,
          website: companySchemaMap.website,
          seller_url: amazonSellerSchemaMap.seller_url,
        },
        lookupSourceRules: {
          'Final Correct': 'WEBSITE_SEARCH_APOLLO_P1:success',
          'No Validator Match (New Process)': 'WEBSITE_SEARCH_APOLLO_P1:fail',
          'Maybe Process': 'WEBSITE_SEARCH_APOLLO_P1:fail',
        }
      },
    },
  },

  'Website Search (Storeleads)': {
    apiEndpoint: 'UpdateCompanies',
    subFunnels: {
      'Record Success/Fail': {
        mode: 'CompanyUpdate',
        requiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          website_status: companySchemaMap.website_status,
          website: companySchemaMap.website,
        },
        updatedApiRequiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          website_status: companySchemaMap.website_status,
          website: companySchemaMap.website,
          seller_url: amazonSellerSchemaMap.seller_url,
        },
        lookupSourceRules: {
          'Final Correct': 'STORELEADS_P1:success',
          'No Validator Match (New Process)': 'STORELEADS_P1:fail',
          'Maybe Process': 'STORELEADS_P1:fail',
        }
      },
    },
  },

  'Website Search (Maybe Process)': {
    apiEndpoint: 'UpdateCompanies',
    mode: 'CompanyUpdate',
    requiredFields: {
      website_status: companySchemaMap.website_status,
      website: companySchemaMap.website,
      amazon_seller_id: companySchemaMap.amazon_seller_id,
      name: companySchemaMap.name
    },
    updatedApiRequiredFields: {
      marketplace: amazonSellerSchemaMap.marketplace,
      amazon_seller_id: companySchemaMap.amazon_seller_id,
      website_status: companySchemaMap.website_status,
      website: companySchemaMap.website,
      name: companySchemaMap.name,
      seller_url: amazonSellerSchemaMap.seller_url,
    },
    lookupSourceRules: {
      // Define different lookup sources based on website_status
      'Final Correct': 'WEBSITE_SEARCH_MANUAL_MAYBE:success',
    }
  },

  'Website Search (Equal SERP)': {
    apiEndpoint: 'UpdateCompanies',
    subFunnels: {
      'Record Pass': {
        mode: 'CompanyUpdate',
        requiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
        },
        updatedApiRequiredFields: {
          marketplace: amazonSellerSchemaMap.marketplace,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          seller_url: amazonSellerSchemaMap.seller_url,
        },
        lookupSourceRules: {
          'DEFAULT:': 'WEBSITE_SEARCH_FULL_SERP_P4'
        }
      },
      'Record Fail': {
        mode: 'CompanyUpdate',
        requiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
        },
        updatedApiRequiredFields: {
          marketplace: amazonSellerSchemaMap.marketplace,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          seller_url: amazonSellerSchemaMap.seller_url,
        },
        lookupSourceRules: {
          'DEFAULT:': 'WEBSITE_SEARCH_FULL_SERP_P4:fail'
        }
      },
      'Record Success/Fail': {
        mode: 'CompanyUpdate',
        requiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          website_status: companySchemaMap.website_status,
          website: companySchemaMap.website,
          seller_url: amazonSellerSchemaMap.seller_url,
        },
        updatedApiRequiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          website_status: companySchemaMap.website_status,
          website: companySchemaMap.website,
        },
        lookupSourceRules: {
          'Final Correct': 'WEBSITE_SEARCH_FULL_SERP_P4:success',
          'No Validator Match (New Process)': 'WEBSITE_SEARCH_FULL_SERP_P4:fail',
          'Maybe Process': 'WEBSITE_SEARCH_FULL_SERP_P4:fail',
        }
      },
    },
  },

  'Prospect Search - 1st Pass Apollo': {
    apiEndpoint: 'UpdateProspects',
    subFunnels: {
      'For Companies': {
        apiEndpoint: 'UpdateCompanies',
        mode: 'CompanyUpdate',
        lookupSourceRules: {
          'DEFAULT:': 'PROSPECT_SEARCH_APOLLO_STEP1_P1'
        },
        updatedApiRequiredFields: {
          marketplace: amazonSellerSchemaMap.marketplace,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          seller_url: amazonSellerSchemaMap.seller_url,
        },
        requiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
        }
      },
      'For Prospects': {
        sourceValue: 'PROSPECT_SEARCH_APOLLO_STEP1_P1',
        apiEndpoint: 'UpdateProspects',
        mode: 'UpdateProspects',
        requiredFields: {
          person_name: prospectSchemaMap.person_name,
          linkedin_url: prospectSchemaMap.person_linkedin,
          website: prospectSchemaMap.website,
          apollo_website: prospectSchemaMap.apollo_website,
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id
        },
        updatedApiRequiredFields: {
          person_name: prospectSchemaMap.person_name,
          linkedin_url: prospectSchemaMap.person_linkedin,
          website: prospectSchemaMap.website,
          apollo_website: prospectSchemaMap.apollo_website,
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          seller_url: amazonSellerSchemaMap.seller_url,
        },
      }
    }
  },
  'Prospect Search - 2nd Pass Apollo': {
    apiEndpoint: 'UpdateProspects',
    subFunnels: {
      'For Companies': {
        apiEndpoint: 'UpdateCompanies',
        mode: 'CompanyUpdate',
        lookupSourceRules: {
          'DEFAULT:': 'PROSPECT_SEARCH_APOLLO_STEP2_P1'
        },
        requiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
        }
      },
      'For Prospects': {
        sourceValue: 'PROSPECT_SEARCH_APOLLO_STEP2_P1',
        apiEndpoint: 'UpdateProspects',
        mode: 'UpdateProspects',
        requiredFields: {
          person_name: prospectSchemaMap.person_name,
          linkedin_url: prospectSchemaMap.person_linkedin,
          website: prospectSchemaMap.website,
          apollo_website: prospectSchemaMap.apollo_website,
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id
        },
        updatedApiRequiredFields: {
          person_name: prospectSchemaMap.person_name,
          linkedin_url: prospectSchemaMap.person_linkedin,
          website: prospectSchemaMap.website,
          apollo_website: prospectSchemaMap.apollo_website,
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          seller_url: amazonSellerSchemaMap.seller_url,
        },
      }
    }
  },


  'Prospect Search - Anymail Finder Funnel': {
    apiEndpoint: 'UpdateProspects',
    subFunnels: {
      'For Companies': {
        apiEndpoint: 'UpdateCompanies',
        mode: 'CompanyUpdate',
        lookupSourceRules: {
          'DEFAULT:': 'PROSPECT_SEARCH_ANYMAIL_FINDER_P1'
        },
        requiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
        },
        updatedApiRequiredFields: {
          marketplace: amazonSellerSchemaMap.marketplace,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
        },
      },
      'For Prospects': {
        sourceValue: 'PROSPECT_SEARCH_ANYMAIL_FINDER_P1',
        apiEndpoint: 'UpdateProspects',
        mode: 'UpdateProspects',
        requiredFields: {
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id
        },
        updatedApiRequiredFields: {
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          seller_url: amazonSellerSchemaMap.seller_url,
        }
      }
    }

  },

  'eMail Harvesting (Amazon SF)': {
    subFunnels: {
      'For Companies': {
        apiEndpoint: 'UpdateCompanies',
        mode: 'CompanyUpdate',
        lookupSourceRules: {
          'DEFAULT:': 'PROSPECT_SEARCH_AMZ_SF_EMAIL_HARVEST_P1'
        },
        requiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
        },
        updatedApiRequiredFields: {
          marketplace: amazonSellerSchemaMap.marketplace,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          seller_url: amazonSellerSchemaMap.seller_url,
        }

      },
      
      'For Prospects': {
        sourceValue: 'PROSPECT_SEARCH_AMZ_SF_EMAIL_HARVEST_P1',
        apiEndpoint: 'UpdateProspects',
        mode: 'UpdateProspects',
        requiredFields: {
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id
        },
        updatedApiRequiredFields: {
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          seller_url: amazonSellerSchemaMap.seller_url,
        }
      }
    }
  },
  'eMail Harvesting (Correct Websites)': {
    apiEndpoint: 'UpdateProspects', // Can be overridden based on sub-funnel
    mode: 'UpdateProspects', // Can be overridden based on sub-funnel
    // For UpdateProspects, use sourceValue instead of lookupSourceRules
    subFunnels: {
      'For Companies': {
        apiEndpoint: 'UpdateCompanies',
        mode: 'CompanyUpdate',
        lookupSourceRules: {
          'DEFAULT:': 'PROSPECT_SEARCH_DOMAIN_EMAIL_HARVEST_P1'
        },
        requiredFields: {
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          name: companySchemaMap.name
        },
        updatedApiRequiredFields: {
          marketplace: amazonSellerSchemaMap.marketplace,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          name: companySchemaMap.name,
          seller_url: amazonSellerSchemaMap.seller_url,
        }
      },
      'For Prospects': {
        sourceValue: 'PROSPECT_SEARCH_DOMAIN_EMAIL_HARVEST_P1',
        apiEndpoint: 'UpdateProspects',
        mode: 'UpdateProspects',
        requiredFields: {
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id
        },
        updatedApiRequiredFields: {
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          seller_url: amazonSellerSchemaMap.seller_url,
        }
      }
    }
  },

  'eMail verification (Bulk Checker 1st Pass)': {
    apiEndpoint: 'UpdateProspects', // Can be overridden based on sub-funnel
    mode: 'UpdateProspects', // Can be overridden based on sub-funnel
    subFunnels: {
      'Record Pass': {
        apiEndpoint: 'UpdateProspects',
        mode: 'UpdateProspects',
        sourceValue: 'EMAIL_VERIFY_BULK_CHECKER_STEP1_P1',
        requiredFields: {
          email: prospectSchemaMap.email,
          amazon_seller_id: companySchemaMap.amazon_seller_id
        },
        updatedApiRequiredFields: {
          email: prospectSchemaMap.email,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          seller_url: amazonSellerSchemaMap.seller_url,
        }
      },
      'Record Success + Fail': {
        apiEndpoint: 'UpdateProspects',
        mode: 'UpdateProspects',
        // Using sourceValueRules instead of a single sourceValue
        sourceValueRules: {
          // Direct matches based on email_staus
          'VERIFIED': 'EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:success',
          'CATCHALL': 'EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:success',
          'GREYLISTING': 'EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:success',
          'FAILED': 'EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:fail',
          'INCONCLUSIVE': 'EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:inconclusive',
        },
        requiredFields: {
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id
        },
        updatedApiRequiredFields: {
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          seller_url: amazonSellerSchemaMap.seller_url,
        }
      }
    }
  },
  'eMail verification (Bulk Checker 2nd Pass)': {
    apiEndpoint: 'UpdateProspects', // Can be overridden based on sub-funnel
    mode: 'UpdateProspects', // Can be overridden based on sub-funnel
    subFunnels: {
      'Record Pass': {
        apiEndpoint: 'UpdateProspects',
        mode: 'UpdateProspects',
        sourceValue: 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1',
        requiredFields: {
          email: prospectSchemaMap.email,
          amazon_seller_id: companySchemaMap.amazon_seller_id
        },
        updatedApiRequiredFields: {
          email: prospectSchemaMap.email,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          seller_url: amazonSellerSchemaMap.seller_url,
        }
      },
      'Record Success + Fail': {
        apiEndpoint: 'UpdateProspects',
        mode: 'UpdateProspects',
        // Using sourceValueRules instead of a single sourceValue
        sourceValueRules: {
          // Direct matches based on email_staus
          'VERIFIED': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:success',
          'CATCHALL': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:success',
          'GREYLISTING': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:success',
          'FAILED': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:fail',
          'INCONCLUSIVE': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:inconclusive',
        },
        requiredFields: {
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id
        },
        updatedApiRequiredFields: {
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id,
          marketplace: amazonSellerSchemaMap.marketplace,
          seller_url: amazonSellerSchemaMap.seller_url,
        }
      }
    }
  },

  'Custom (change manually before run)': {
    // apiEndpoint: 'UpdateProspects', // Can be overridden based on sub-funnel
    // mode: 'UpdateProspects', // Can be overridden based on sub-funnel
    subFunnels: {
      'Custom (Update Prospects)': {
        apiEndpoint: 'UpdateProspects',
        mode: 'UpdateProspects',
        sourceValueRules: {
          // Direct matches based on email_staus
          // 'VERIFIED': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:success',
          'CATCHALL': 'EMAIL_VERIFY_BULK_CHECKER_STEP1_P1:success',
          // 'GREYLISTING': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:success',
          // 'FAILED': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:fail',
          // 'INCONCLUSIVE': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:inconclusive',
        },
        requiredFields: {
          email: prospectSchemaMap.email,
          amazon_seller_id: companySchemaMap.amazon_seller_id
        }
      },
      'Record Success + Fail': {
        apiEndpoint: 'UpdateCompanies',
        mode: 'CompanyUpdate',
        // Using sourceValueRules instead of a single sourceValue
        sourceValueRules: {
          // Direct matches based on email_staus
          'VERIFIED': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:success',
          'CATCHALL': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:success',
          'GREYLISTING': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:success',
          'FAILED': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:fail',
          'INCONCLUSIVE': 'EMAIL_VERIFY_BULK_CHECKER_STEP2_P1:inconclusive',
        },
        requiredFields: {
          email: prospectSchemaMap.email,
          email_status: prospectSchemaMap.email_status,
          amazon_seller_id: companySchemaMap.amazon_seller_id
        }
      }
    }
  }
};

module.exports = funnelConfig;
