/**
 * Utility to access Prisma model metadata at runtime
 * This module provides functions to introspect Prisma models
 * dynamically using Prisma's runtime data model
 */

const prisma = require("../database/prisma/getPrismaClient");

/**
 * Utility to access Prisma model metadata at runtime
 */
class PrismaModelIntrospector {
  constructor() {
    this.prisma = prisma;

    if (!prisma._runtimeDataModel || !prisma._runtimeDataModel.models) {
      throw new Error("Could not access Prisma runtime data model");
    }

    this.modelMap = prisma._runtimeDataModel.models;
  }

  /**
   * Get all model names
   * @returns {string[]} Array of model names
   */
  getModelNames() {
    return Object.keys(this.modelMap);
  }

  /**
   * Get field types for a specific model
   * @param {string} modelName - The Prisma model name
   * @returns {Object} Object mapping field names to their types
   */
  getModelFieldTypes(modelName) {
    const model = this.modelMap[modelName];
    if (!model) {
      throw new Error(`Model ${modelName} not found in Prisma schema`);
    }

    const fieldTypes = {};
    model.fields.forEach((field) => {
      fieldTypes[field.name] = field.type;
    });

    return fieldTypes;
  }

  /**
   * Get field details including type, requirements, defaults, etc.
   * @param {string} modelName - The Prisma model name
   * @returns {Object} Object mapping field names to their full details
   */
  getModelFieldDetails(modelName) {
    const model = this.modelMap[modelName];
    if (!model) {
      throw new Error(`Model ${modelName} not found in Prisma schema`);
    }

    const fieldDetails = {};
    model.fields.forEach((field) => {
      fieldDetails[field.name] = {
        type: field.type,
        isRequired: field.isRequired,
        isUnique: field.isUnique,
        isId: field.isId,
        hasDefaultValue: field.hasDefaultValue,
        default: field.default,
        isList: field.isList,
        kind: field.kind,
        relationName: field.relationName,
      };
    });

    return fieldDetails;
  }

  /**
   * Get all scalar fields for a model (excluding relations)
   * @param {string} modelName - The Prisma model name
   * @returns {string[]} Array of scalar field names
   */
  getScalarFields(modelName) {
    const model = this.modelMap[modelName];
    if (!model) {
      throw new Error(`Model ${modelName} not found in Prisma schema`);
    }

    return model.fields
      .filter((field) => field.kind === "scalar")
      .map((field) => field.name);
  }

  /**
   * Get all relation fields for a model
   * @param {string} modelName - The Prisma model name
   * @returns {string[]} Array of relation field names
   */
  getRelationFields(modelName) {
    const model = this.modelMap[modelName];
    if (!model) {
      throw new Error(`Model ${modelName} not found in Prisma schema`);
    }

    return model.fields
      .filter((field) => field.kind === "object")
      .map((field) => field.name);
  }

  /**
   * Type cast a value based on the field type in the model
   * @param {string} modelName - The Prisma model name
   * @param {string} fieldName - The field name
   * @param {any} value - The value to cast
   * @returns {any} The value cast to the appropriate type
   * @throws {Error} If casting fails or the field/model doesn't exist
   */
  typeCastValue(modelName, fieldName, value) {
    // Handle empty strings, null, undefined - return null for all these cases
    if (value === null || value === undefined || value === "") {
      return null;
    }

    const field = this.getFieldInfo(modelName, fieldName);

    if (!field) {
      // If field is not found, return the value as it is
      return value;
    }

    // Skip casting for relation fields
    if (field.kind === "object") {
      return value;
    }

    // Handle list types differently
    if (field.isList) {
      if (!Array.isArray(value)) {
        throw new Error(
          `Field ${modelName}.${fieldName} expects an array, got: ${typeof value}`
        );
      }

      return value.map((item) => {
        // If item is empty, return null
        if (item === null || item === undefined || item === "") {
          return null;
        }
        return this.castSingleValue(field.type, item, modelName, fieldName);
      });
    }

    return this.castSingleValue(field.type, value, modelName, fieldName);
  }

  /**
   * Helper method to cast a single value to the appropriate type
   * @private
   */
  castSingleValue(type, value, modelName, fieldName) {
    // Handle empty strings and other unsuitable values
    if (value === "" || value === null || value === undefined) {
      return null;
    }

    switch (type) {
      case "Int":
        if (value === "" || value === null || value === undefined) return null;
        const intVal = parseInt(value, 10);
        if (isNaN(intVal)) {
          throw new Error(
            `Invalid integer value for field ${modelName}.${fieldName}: "${value}"`
          );
        }
        return intVal;

      case "Float":
        if (value === "" || value === null || value === undefined) return null;
        const floatVal = parseFloat(value);
        if (isNaN(floatVal)) {
          throw new Error(
            `Invalid float value for field ${modelName}.${fieldName}: "${value}"`
          );
        }
        return floatVal;

      case "Boolean":
        if (typeof value === "boolean") return value;
        if (value === "true" || value === "1" || value === 1) return true;
        if (value === "false" || value === "0" || value === 0 || value === "")
          return false;
        throw new Error(
          `Invalid boolean value for field ${modelName}.${fieldName}: "${value}"`
        );

      case "String":
        if (value === "" || value === null || value === undefined) return null;
        return String(value);

      case "DateTime":
        if (value === "" || value === null || value === undefined) return null;
        const dateVal = new Date(value);
        if (isNaN(dateVal.getTime())) {
          throw new Error(
            `Invalid date value for field ${modelName}.${fieldName}: "${value}"`
          );
        }
        return dateVal;

      case "Json":
        if (typeof value === "object") return value;
        if (value === "" || value === null || value === undefined) return null;

        try {
          return JSON.parse(value);
        } catch (e) {
          throw new Error(
            `Invalid JSON value for field ${modelName}.${fieldName}: "${value}"`
          );
        }

      default:
        // For enums or other types, return as is
        return value;
    }
  }

  /**
   * Helper to get complete field info for a single field
   * @private
   */
  getFieldInfo(modelName, fieldName) {
    const model = this.modelMap[modelName];
    if (!model) {
      throw new Error(`Model ${modelName} not found in Prisma schema`);
    }

    const field = model.fields.find((f) => f.name === fieldName);
    if (!field) {
      throw new Error(`Field ${fieldName} not found in model ${modelName}`);
    }

    return field;
  }

  /**
   * Get complete model definition
   * @param {string} modelName - The Prisma model name
   * @returns {Object} Complete model definition
   */
  getModelDefinition(modelName) {
    return this.modelMap[modelName];
  }
  /**
   * Transform data based on model definition
   * @param {Object} data - The data to transform
   * @param {string} modelName - The Prisma model name
   * @returns {Object} Transformed data
   */
  transformData(data, modelName) {
    const transformedData = { ...data };
    console.log("data", data);

    // Initialize errors array if it doesn't exist
    if (!transformedData.errors) {
      transformedData.errors = [];
    }

    Object.keys(data).forEach((key) => {
      console.log(
        "key",
        key,
        "data[key]",
        data[key],
        "modelName",
        modelName,
        "type",
        typeof data[key]
      );

      try {
        transformedData[key] = this.typeCastValue(modelName, key, data[key]);
        console.log("Transformed data:", transformedData[key]);
      } catch (error) {
        console.warn(`Error transforming ${key}: ${error.message}`);

        // Add the error to the errors array
        transformedData.errors.push(error.message);

        // Keep the original value
        transformedData[key] = data[key];
      }
    });

    return transformedData;
  }
}

// Try to create a singleton instance
let introspector;
try {
  introspector = new PrismaModelIntrospector();
} catch (error) {
  console.warn(
    `Warning: Could not initialize Prisma Model Introspector: ${error.message}`
  );
  introspector = null;
}

module.exports = introspector;
