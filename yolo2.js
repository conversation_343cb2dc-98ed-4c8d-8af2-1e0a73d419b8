const fs = require("fs");
const path = require("path");
const csv = require("csvtojson");
const { Parser } = require("json2csv");

async function combineOutputCsvs(inputDir, outputFilePath) {
  try {
    // Get all CSV files from the input directory that end with "output.csv"
    const csvFiles = fs
      .readdirSync(inputDir)
      .filter((file) => file.toLowerCase().endsWith("output.csv"))
      .map((file) => path.join(inputDir, file));

    if (csvFiles.length === 0) {
      console.error(`No output CSV files found in "${inputDir}".`);
      return;
    }

    console.log(`Found ${csvFiles.length} output CSV files to combine.`);
    console.log("Files to combine:", csvFiles);

    // Array to hold all rows from all files
    let allRows = [];
    let allHeaders = new Set();

    // First pass: collect all possible headers across all files
    for (const file of csvFiles) {
      console.log(`Scanning headers in file: ${file}`);
      const rows = await csv().fromFile(file);

      if (rows.length > 0) {
        Object.keys(rows[0]).forEach((header) => allHeaders.add(header));
      }
    }

    // Make sure 'scraped_websites' is included in the headers
    allHeaders.add("scraped_websites");

    // Convert Set to Array for the parser
    const headers = Array.from(allHeaders);
    console.log("All headers that will be included:", headers);

    // Second pass: process all rows and ensure each row has all headers
    for (const file of csvFiles) {
      console.log(`Processing rows from file: ${file}`);
      const rows = await csv().fromFile(file);

      // Process each row to ensure it has all headers
      for (const row of rows) {
        const completeRow = {};

        // Initialize all headers with empty values
        headers.forEach((header) => {
          completeRow[header] = row[header] || "";
        });

        allRows.push(completeRow);
      }

      console.log(`Added ${rows.length} rows from ${path.basename(file)}`);
    }

    console.log(`Total rows to write: ${allRows.length}`);

    // Write the combined data to the output file
    if (allRows.length > 0) {
      const parser = new Parser({ fields: headers });
      const csvData = parser.parse(allRows);
      fs.writeFileSync(outputFilePath, csvData);
      console.log(`Combined CSV written to: ${outputFilePath}`);
    } else {
      console.log("No data to write.");
    }

    console.log("CSV combination completed successfully!");
  } catch (error) {
    console.error("Error combining CSV files:", error);
  }
}

// Get the input directory and output file path from command line arguments or use defaults
const inputDir = process.argv[2] || path.join("./split_output");
const outputFilePath = process.argv[3] || path.join("./29 JULY_em-combined_output.csv");

combineOutputCsvs(inputDir, outputFilePath);
