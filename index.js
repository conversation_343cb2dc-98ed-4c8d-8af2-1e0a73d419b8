const express = require("express");
const cors = require("cors");

require("dotenv").config();
require("newrelic");

const { specs, swaggerUi } = require("./swagger");
const path = require("path");

const app = express();
const PORT = 8000;

// Middleware
app.use(
  cors({
    origin: "*",
    methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
    preflightContinue: false,
    optionsSuccessStatus: 204,
    exposedHeaders: ["Content-Type", "Authorization", "Content-Disposition"],
  })
);
app.use(express.json());
// app.use(newrelic.expressMiddleware());
app.use(
  "/api-docs",
  (req, res, next) => {
    const protocol = req.protocol;
    const host = req.get("host");

    specs.servers = [
      {
        url: `${protocol}://${host}`,
        description: "Current server",
      },
    ];
    next();
  },
  swaggerUi.serve,
  swaggerUi.setup(specs, { explorer: true })
);
app.use("/examples", express.static(path.join(__dirname, "public/examples")));
// Import routes
const routes = require("./routes");
const lexRoutes = require("./routes/lex");
app.use(routes);
app.use(lexRoutes);

//LeadJob update and slack Notification worker
// startWorkerWithCron();

// Start MX Record Scheduler (only in production/staging/development environments)
const nodeEnv = process.env.NODE_ENV;
if (nodeEnv && nodeEnv !== 'test') {
  let jobWorker = require("./worker/jobWorker");
  const reviewUpdater = require("./services/lexReviewsChecker/reviewUpdater");

  // Conditionally start reviewChecker based on environment variables
  const ENABLE_REVIEW_CHECKER = process.env.ENABLE_REVIEW_CHECKER !== 'false';
  if (ENABLE_REVIEW_CHECKER) {
    console.log("🔄 Starting reviewChecker service...");
    require("./services/lexReviewsChecker/reviewChecker");
  } else {
    console.log("🚫 ReviewChecker service disabled via environment variable");
  }

  const {
    emailThreadUpdater,
    emailThreadCronJob,
  } = require("./services/saveEmails/emailThreadUpdater");
  jobWorker = require("./worker/jobWorker");
  const {
    startWorkerWithCron,
  } = require("./services/generateLeads/workers/jobUpdateWorker");
  // Import MX Record Scheduler
  const mxScheduler = require("./services/MXRecordChecker/scheduler");

  mxScheduler.start();

} else {
  console.log(`🚫 Skipping MX Record Scheduler startup (NODE_ENV: ${nodeEnv || 'not set'})`);
}

const ENABLE_REVIEW_CHECKER = process.env.ENABLE_REVIEW_CHECKER !== 'false';
if (ENABLE_REVIEW_CHECKER) {
  console.log("🔄 Starting reviewChecker service...");
  require("./services/lexReviewsChecker/reviewChecker");
} else {
  console.log("🚫 ReviewChecker service disabled via environment variable");
}

app.listen(PORT, () =>
  console.log(`Server running on http://localhost:${PORT}`)
);
