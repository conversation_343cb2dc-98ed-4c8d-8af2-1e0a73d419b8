<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <!-- Health check configuration for Azure App Service -->
    <rewrite>
      <rules>
        <!-- Health check rule for Azure -->
        <rule name="Health Check" stopProcessing="true">
          <match url="^health$" />
          <action type="Rewrite" url="/health" />
        </rule>
        
        <!-- Main application rule -->
        <rule name="DynamicContent">
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True"/>
          </conditions>
          <action type="Rewrite" url="server.js"/>
        </rule>
      </rules>
    </rewrite>
    
    <!-- Configure health check monitoring -->
    <httpErrors>
      <remove statusCode="404" subStatusCode="-1" />
      <error statusCode="404" prefixLanguageFilePath="" path="/health" responseMode="ExecuteURL" />
    </httpErrors>
    
    <!-- Security headers -->
    <httpProtocol>
      <customHeaders>
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-Frame-Options" value="DENY" />
        <add name="X-XSS-Protection" value="1; mode=block" />
      </customHeaders>
    </httpProtocol>
  </system.webServer>
</configuration>
